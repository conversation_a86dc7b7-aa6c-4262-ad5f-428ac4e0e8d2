<?php

namespace App\Controllers\Api;

use App\Models\StudyProgramModel;

class StudyProgramController extends BaseApiController
{
    protected $studyProgramModel;

    public function __construct()
    {
        parent::__construct();
        $this->studyProgramModel = new StudyProgramModel();
    }

    /**
     * Get all study programs
     */
    public function index()
    {
        try {
            $page = $this->request->getGet('page') ?? 1;
            $perPage = $this->request->getGet('per_page') ?? 10;
            $search = $this->request->getGet('search');
            $facultyId = $this->request->getGet('faculty_id');

            $query = $this->studyProgramModel;
            
            if ($search) {
                $query = $query->like('name', $search)
                              ->orLike('code', $search);
            }

            if ($facultyId) {
                $query = $query->where('faculty_id', $facultyId);
            }

            $total = $query->countAllResults(false);
            $studyPrograms = $query->findAll($perPage, ($page - 1) * $perPage);

            $result = [
                'data' => $studyPrograms,
                'pagination' => [
                    'current_page' => (int)$page,
                    'per_page' => (int)$perPage,
                    'total' => $total,
                    'total_pages' => ceil($total / $perPage),
                    'has_next' => $page < ceil($total / $perPage),
                    'has_prev' => $page > 1
                ]
            ];

            return $this->respondSuccess($result, 'Study programs retrieved successfully');

        } catch (\Exception $e) {
            return $this->respondError('Failed to retrieve study programs: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get study program by ID
     */
    public function show($id = null)
    {
        try {
            if (!$id) {
                return $this->respondError('Study program ID is required', 400);
            }

            $studyProgram = $this->studyProgramModel->find($id);
            
            if (!$studyProgram) {
                return $this->respondNotFound('Study program not found');
            }

            return $this->respondSuccess($studyProgram, 'Study program retrieved successfully');

        } catch (\Exception $e) {
            return $this->respondError('Failed to retrieve study program: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Create new study program
     */
    public function create()
    {
        try {
            $validation = $this->validateRequest([
                'name' => 'required|min_length[3]|max_length[100]',
                'code' => 'required|min_length[2]|max_length[10]|is_unique[study_programs.code]',
                'faculty_id' => 'required|integer',
                'degree_level' => 'required|in_list[D3,D4,S1,S2,S3]',
                'description' => 'permit_empty|max_length[500]'
            ]);

            if ($validation) {
                return $validation;
            }

            $data = [
                'name' => $this->request->getPost('name'),
                'code' => $this->request->getPost('code'),
                'faculty_id' => $this->request->getPost('faculty_id'),
                'degree_level' => $this->request->getPost('degree_level'),
                'description' => $this->request->getPost('description'),
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $studyProgramId = $this->studyProgramModel->insert($data);

            if (!$studyProgramId) {
                return $this->respondError('Failed to create study program', 500);
            }

            $studyProgram = $this->studyProgramModel->find($studyProgramId);

            return $this->respondSuccess($studyProgram, 'Study program created successfully', 201);

        } catch (\Exception $e) {
            return $this->respondError('Failed to create study program: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Update study program
     */
    public function update($id = null)
    {
        try {
            if (!$id) {
                return $this->respondError('Study program ID is required', 400);
            }

            $studyProgram = $this->studyProgramModel->find($id);
            if (!$studyProgram) {
                return $this->respondNotFound('Study program not found');
            }

            $rules = [
                'name' => 'permit_empty|min_length[3]|max_length[100]',
                'code' => "permit_empty|min_length[2]|max_length[10]|is_unique[study_programs.code,id,$id]",
                'faculty_id' => 'permit_empty|integer',
                'degree_level' => 'permit_empty|in_list[D3,D4,S1,S2,S3]',
                'description' => 'permit_empty|max_length[500]',
                'is_active' => 'permit_empty|in_list[0,1]'
            ];

            $validation = $this->validateRequest($rules);
            if ($validation) {
                return $validation;
            }

            $data = [];
            $fields = ['name', 'code', 'faculty_id', 'degree_level', 'description', 'is_active'];
            
            foreach ($fields as $field) {
                $value = $this->request->getPost($field);
                if ($value !== null) {
                    $data[$field] = $value;
                }
            }

            $data['updated_at'] = date('Y-m-d H:i:s');

            $this->studyProgramModel->update($id, $data);

            $updatedStudyProgram = $this->studyProgramModel->find($id);

            return $this->respondSuccess($updatedStudyProgram, 'Study program updated successfully');

        } catch (\Exception $e) {
            return $this->respondError('Failed to update study program: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Delete study program
     */
    public function delete($id = null)
    {
        try {
            if (!$id) {
                return $this->respondError('Study program ID is required', 400);
            }

            $studyProgram = $this->studyProgramModel->find($id);
            if (!$studyProgram) {
                return $this->respondNotFound('Study program not found');
            }

            $this->studyProgramModel->delete($id);

            return $this->respondSuccess(null, 'Study program deleted successfully');

        } catch (\Exception $e) {
            return $this->respondError('Failed to delete study program: ' . $e->getMessage(), 500);
        }
    }
}
