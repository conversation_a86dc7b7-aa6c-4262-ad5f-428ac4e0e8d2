<?php

namespace App\Helpers;

/**
 * Admin Menu Helper
 * 
 * Manages the admin navigation menu system with dynamic menu generation,
 * permissions, and hierarchical structure
 */
class AdminMenuHelper
{
    /**
     * Get the complete admin menu structure
     * 
     * @param string $currentPage Current active page
     * @param array $userPermissions User permissions array
     * @return array Menu structure
     */
    public static function getMenuStructure($currentPage = '', $userPermissions = [])
    {
        $menu = [
            // Dashboard Section
            [
                'type' => 'item',
                'title' => 'Dashboard',
                'icon' => 'ri-home-smile-line',
                'url' => 'admin',
                'page' => 'dashboard',
                'permission' => 'admin.dashboard.view',
                'badge' => null,
                'children' => []
            ],

            // User Management Section
            [
                'type' => 'header',
                'title' => 'User Management',
                'permission' => 'admin.users.view'
            ],
            [
                'type' => 'item',
                'title' => 'Users',
                'icon' => 'ri-user-line',
                'url' => 'admin/users',
                'page' => 'users',
                'permission' => 'admin.users.view',
                'badge' => null,
                'children' => [
                    [
                        'title' => 'All Users',
                        'url' => 'admin/users',
                        'permission' => 'admin.users.view'
                    ],
                    [
                        'title' => 'Add User',
                        'url' => 'admin/users/create',
                        'permission' => 'admin.users.create'
                    ],
                    [
                        'title' => 'User Roles',
                        'url' => 'admin/users/roles',
                        'permission' => 'admin.users.roles'
                    ]
                ]
            ],
            [
                'type' => 'item',
                'title' => 'Roles & Permissions',
                'icon' => 'ri-shield-user-line',
                'url' => 'admin/roles',
                'page' => 'roles',
                'permission' => 'admin.roles.view',
                'badge' => null,
                'children' => []
            ],

            // Academic Management Section
            [
                'type' => 'header',
                'title' => 'Academic Management',
                'permission' => 'admin.academic.view'
            ],
            [
                'type' => 'item',
                'title' => 'Courses',
                'icon' => 'ri-book-open-line',
                'url' => 'admin/courses',
                'page' => 'courses',
                'permission' => 'admin.courses.view',
                'badge' => null,
                'children' => [
                    [
                        'title' => 'All Courses',
                        'url' => 'admin/courses',
                        'permission' => 'admin.courses.view'
                    ],
                    [
                        'title' => 'Add Course',
                        'url' => 'admin/courses/create',
                        'permission' => 'admin.courses.create'
                    ],
                    [
                        'title' => 'Course Categories',
                        'url' => 'admin/courses/categories',
                        'permission' => 'admin.courses.categories'
                    ]
                ]
            ],
            [
                'type' => 'item',
                'title' => 'CPMK',
                'icon' => 'ri-target-line',
                'url' => 'admin/cpmk',
                'page' => 'cpmk',
                'permission' => 'admin.cpmk.view',
                'badge' => null,
                'children' => [
                    [
                        'title' => 'All CPMK',
                        'url' => 'admin/cpmk',
                        'permission' => 'admin.cpmk.view'
                    ],
                    [
                        'title' => 'Add CPMK',
                        'url' => 'admin/cpmk/create',
                        'permission' => 'admin.cpmk.create'
                    ],
                    [
                        'title' => 'CPMK Mapping',
                        'url' => 'admin/cpmk/mapping',
                        'permission' => 'admin.cpmk.mapping'
                    ]
                ]
            ],
            [
                'type' => 'item',
                'title' => 'CPL',
                'icon' => 'ri-focus-3-line',
                'url' => 'admin/cpl',
                'page' => 'cpl',
                'permission' => 'admin.cpl.view',
                'badge' => null,
                'children' => [
                    [
                        'title' => 'All CPL',
                        'url' => 'admin/cpl',
                        'permission' => 'admin.cpl.view'
                    ],
                    [
                        'title' => 'Add CPL',
                        'url' => 'admin/cpl/create',
                        'permission' => 'admin.cpl.create'
                    ],
                    [
                        'title' => 'CPL Assessment',
                        'url' => 'admin/cpl/assessment',
                        'permission' => 'admin.cpl.assessment'
                    ]
                ]
            ],
            [
                'type' => 'item',
                'title' => 'RPS Templates',
                'icon' => 'ri-file-text-line',
                'url' => 'admin/rps-templates',
                'page' => 'rps-templates',
                'permission' => 'admin.rps.view',
                'badge' => 'New',
                'children' => []
            ],

            // Content Management Section
            [
                'type' => 'header',
                'title' => 'Content Management',
                'permission' => 'admin.content.view'
            ],
            [
                'type' => 'item',
                'title' => 'Learning Materials',
                'icon' => 'ri-folder-open-line',
                'url' => 'admin/materials',
                'page' => 'materials',
                'permission' => 'admin.materials.view',
                'badge' => null,
                'children' => []
            ],
            [
                'type' => 'item',
                'title' => 'Assessments',
                'icon' => 'ri-file-list-3-line',
                'url' => 'admin/assessments',
                'page' => 'assessments',
                'permission' => 'admin.assessments.view',
                'badge' => null,
                'children' => []
            ],

            // Reports & Analytics Section
            [
                'type' => 'header',
                'title' => 'Reports & Analytics',
                'permission' => 'admin.reports.view'
            ],
            [
                'type' => 'item',
                'title' => 'Reports',
                'icon' => 'ri-bar-chart-line',
                'url' => 'admin/reports',
                'page' => 'reports',
                'permission' => 'admin.reports.view',
                'badge' => null,
                'children' => [
                    [
                        'title' => 'Course Reports',
                        'url' => 'admin/reports/courses',
                        'permission' => 'admin.reports.courses'
                    ],
                    [
                        'title' => 'User Reports',
                        'url' => 'admin/reports/users',
                        'permission' => 'admin.reports.users'
                    ],
                    [
                        'title' => 'CPMK Reports',
                        'url' => 'admin/reports/cpmk',
                        'permission' => 'admin.reports.cpmk'
                    ],
                    [
                        'title' => 'CPL Reports',
                        'url' => 'admin/reports/cpl',
                        'permission' => 'admin.reports.cpl'
                    ]
                ]
            ],
            [
                'type' => 'item',
                'title' => 'Analytics',
                'icon' => 'ri-line-chart-line',
                'url' => 'admin/analytics',
                'page' => 'analytics',
                'permission' => 'admin.analytics.view',
                'badge' => null,
                'children' => []
            ],

            // System Section
            [
                'type' => 'header',
                'title' => 'System',
                'permission' => 'admin.system.view'
            ],
            [
                'type' => 'item',
                'title' => 'Settings',
                'icon' => 'ri-settings-3-line',
                'url' => 'admin/settings',
                'page' => 'settings',
                'permission' => 'admin.settings.view',
                'badge' => null,
                'children' => [
                    [
                        'title' => 'General Settings',
                        'url' => 'admin/settings/general',
                        'permission' => 'admin.settings.general'
                    ],
                    [
                        'title' => 'Email Settings',
                        'url' => 'admin/settings/email',
                        'permission' => 'admin.settings.email'
                    ],
                    [
                        'title' => 'Backup & Restore',
                        'url' => 'admin/settings/backup',
                        'permission' => 'admin.settings.backup'
                    ]
                ]
            ],
            [
                'type' => 'item',
                'title' => 'System Logs',
                'icon' => 'ri-file-list-line',
                'url' => 'admin/logs',
                'page' => 'logs',
                'permission' => 'admin.logs.view',
                'badge' => null,
                'children' => []
            ],

            // Logout
            [
                'type' => 'item',
                'title' => 'Logout',
                'icon' => 'ri-logout-box-line',
                'url' => 'admin/logout',
                'page' => 'logout',
                'permission' => null,
                'badge' => null,
                'children' => [],
                'onclick' => "return confirm('Are you sure you want to logout?')"
            ]
        ];

        // Filter menu based on permissions
        return self::filterMenuByPermissions($menu, $userPermissions, $currentPage);
    }

    /**
     * Filter menu items based on user permissions
     * 
     * @param array $menu Menu structure
     * @param array $userPermissions User permissions
     * @param string $currentPage Current page
     * @return array Filtered menu
     */
    private static function filterMenuByPermissions($menu, $userPermissions, $currentPage)
    {
        $filteredMenu = [];

        foreach ($menu as $item) {
            // Check if user has permission for this menu item
            if (isset($item['permission']) && $item['permission'] && !in_array($item['permission'], $userPermissions)) {
                continue;
            }

            // Set active state
            if (isset($item['page']) && $item['page'] === $currentPage) {
                $item['active'] = true;
            }

            // Filter children if they exist
            if (isset($item['children']) && !empty($item['children'])) {
                $item['children'] = array_filter($item['children'], function($child) use ($userPermissions) {
                    return !isset($child['permission']) || !$child['permission'] || in_array($child['permission'], $userPermissions);
                });
            }

            $filteredMenu[] = $item;
        }

        return $filteredMenu;
    }

    /**
     * Get breadcrumb navigation
     * 
     * @param string $currentPage Current page
     * @return array Breadcrumb items
     */
    public static function getBreadcrumb($currentPage)
    {
        $breadcrumbs = [
            'dashboard' => [
                ['title' => 'Dashboard', 'url' => 'admin']
            ],
            'users' => [
                ['title' => 'Dashboard', 'url' => 'admin'],
                ['title' => 'Users', 'url' => 'admin/users']
            ],
            'courses' => [
                ['title' => 'Dashboard', 'url' => 'admin'],
                ['title' => 'Courses', 'url' => 'admin/courses']
            ],
            'cpmk' => [
                ['title' => 'Dashboard', 'url' => 'admin'],
                ['title' => 'CPMK', 'url' => 'admin/cpmk']
            ],
            'cpl' => [
                ['title' => 'Dashboard', 'url' => 'admin'],
                ['title' => 'CPL', 'url' => 'admin/cpl']
            ],
            'reports' => [
                ['title' => 'Dashboard', 'url' => 'admin'],
                ['title' => 'Reports', 'url' => 'admin/reports']
            ],
            'settings' => [
                ['title' => 'Dashboard', 'url' => 'admin'],
                ['title' => 'Settings', 'url' => 'admin/settings']
            ]
        ];

        return $breadcrumbs[$currentPage] ?? [['title' => 'Dashboard', 'url' => 'admin']];
    }

    /**
     * Get menu statistics for dashboard
     * 
     * @return array Menu statistics
     */
    public static function getMenuStats()
    {
        return [
            'total_menu_items' => 15,
            'active_sections' => 6,
            'permission_protected' => 12,
            'last_updated' => date('Y-m-d H:i:s')
        ];
    }
}
