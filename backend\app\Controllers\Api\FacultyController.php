<?php

namespace App\Controllers\Api;

use App\Models\FacultyModel;

class FacultyController extends BaseApiController
{
    protected $facultyModel;

    public function __construct()
    {
        parent::__construct();
        $this->facultyModel = new FacultyModel();
    }

    /**
     * Get all faculties
     */
    public function index()
    {
        try {
            $page = $this->request->getGet('page') ?? 1;
            $perPage = $this->request->getGet('per_page') ?? 10;
            $search = $this->request->getGet('search');

            $query = $this->facultyModel;
            
            if ($search) {
                $query = $query->like('name', $search)
                              ->orLike('code', $search);
            }

            $total = $query->countAllResults(false);
            $faculties = $query->findAll($perPage, ($page - 1) * $perPage);

            $result = [
                'data' => $faculties,
                'pagination' => [
                    'current_page' => (int)$page,
                    'per_page' => (int)$perPage,
                    'total' => $total,
                    'total_pages' => ceil($total / $perPage),
                    'has_next' => $page < ceil($total / $perPage),
                    'has_prev' => $page > 1
                ]
            ];

            return $this->respondSuccess($result, 'Faculties retrieved successfully');

        } catch (\Exception $e) {
            return $this->respondError('Failed to retrieve faculties: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get faculty by ID
     */
    public function show($id = null)
    {
        try {
            if (!$id) {
                return $this->respondError('Faculty ID is required', 400);
            }

            $faculty = $this->facultyModel->find($id);
            
            if (!$faculty) {
                return $this->respondNotFound('Faculty not found');
            }

            return $this->respondSuccess($faculty, 'Faculty retrieved successfully');

        } catch (\Exception $e) {
            return $this->respondError('Failed to retrieve faculty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Create new faculty
     */
    public function create()
    {
        try {
            $validation = $this->validateRequest([
                'name' => 'required|min_length[3]|max_length[100]',
                'code' => 'required|min_length[2]|max_length[10]|is_unique[faculties.code]',
                'description' => 'permit_empty|max_length[500]'
            ]);

            if ($validation) {
                return $validation;
            }

            $data = [
                'name' => $this->request->getPost('name'),
                'code' => $this->request->getPost('code'),
                'description' => $this->request->getPost('description'),
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $facultyId = $this->facultyModel->insert($data);

            if (!$facultyId) {
                return $this->respondError('Failed to create faculty', 500);
            }

            $faculty = $this->facultyModel->find($facultyId);

            return $this->respondSuccess($faculty, 'Faculty created successfully', 201);

        } catch (\Exception $e) {
            return $this->respondError('Failed to create faculty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Update faculty
     */
    public function update($id = null)
    {
        try {
            if (!$id) {
                return $this->respondError('Faculty ID is required', 400);
            }

            $faculty = $this->facultyModel->find($id);
            if (!$faculty) {
                return $this->respondNotFound('Faculty not found');
            }

            $rules = [
                'name' => 'permit_empty|min_length[3]|max_length[100]',
                'code' => "permit_empty|min_length[2]|max_length[10]|is_unique[faculties.code,id,$id]",
                'description' => 'permit_empty|max_length[500]',
                'is_active' => 'permit_empty|in_list[0,1]'
            ];

            $validation = $this->validateRequest($rules);
            if ($validation) {
                return $validation;
            }

            $data = [];
            $fields = ['name', 'code', 'description', 'is_active'];
            
            foreach ($fields as $field) {
                $value = $this->request->getPost($field);
                if ($value !== null) {
                    $data[$field] = $value;
                }
            }

            $data['updated_at'] = date('Y-m-d H:i:s');

            $this->facultyModel->update($id, $data);

            $updatedFaculty = $this->facultyModel->find($id);

            return $this->respondSuccess($updatedFaculty, 'Faculty updated successfully');

        } catch (\Exception $e) {
            return $this->respondError('Failed to update faculty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Delete faculty
     */
    public function delete($id = null)
    {
        try {
            if (!$id) {
                return $this->respondError('Faculty ID is required', 400);
            }

            $faculty = $this->facultyModel->find($id);
            if (!$faculty) {
                return $this->respondNotFound('Faculty not found');
            }

            $this->facultyModel->delete($id);

            return $this->respondSuccess(null, 'Faculty deleted successfully');

        } catch (\Exception $e) {
            return $this->respondError('Failed to delete faculty: ' . $e->getMessage(), 500);
        }
    }
}
