<?php

namespace App\Models;

use CodeIgniter\Model;

class FacultyModel extends Model
{
    protected $table = 'faculties';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'name',
        'code',
        'description',
        'is_active'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'name' => 'required|min_length[3]|max_length[100]',
        'code' => 'required|min_length[2]|max_length[10]|is_unique[faculties.code,id,{id}]',
        'description' => 'permit_empty|max_length[500]',
        'is_active' => 'in_list[0,1]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'Faculty name is required',
            'min_length' => 'Faculty name must be at least 3 characters',
            'max_length' => 'Faculty name cannot exceed 100 characters'
        ],
        'code' => [
            'required' => 'Faculty code is required',
            'min_length' => 'Faculty code must be at least 2 characters',
            'max_length' => 'Faculty code cannot exceed 10 characters',
            'is_unique' => 'Faculty code already exists'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;
    protected $allowCallbacks = true;

    /**
     * Get active faculties
     */
    public function getActiveFaculties()
    {
        return $this->where('is_active', 1)->findAll();
    }

    /**
     * Get faculty with study programs
     */
    public function getFacultyWithStudyPrograms($facultyId)
    {
        $faculty = $this->find($facultyId);
        if (!$faculty) {
            return null;
        }

        $studyProgramModel = new StudyProgramModel();
        $faculty['study_programs'] = $studyProgramModel->where('faculty_id', $facultyId)
                                                       ->where('is_active', 1)
                                                       ->findAll();

        return $faculty;
    }

    /**
     * Get faculty statistics
     */
    public function getFacultyStats()
    {
        $total = $this->countAllResults();
        $active = $this->where('is_active', 1)->countAllResults();
        $inactive = $this->where('is_active', 0)->countAllResults();

        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $inactive
        ];
    }
}
