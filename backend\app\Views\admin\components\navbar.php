<?php
use App\Helpers\NavigationHelper;
use App\Helpers\AdminMenuHelper;

// Get current page and quick actions
$currentPage = $page ?? 'dashboard';
$quickActions = NavigationHelper::getQuickActions($currentPage);
$menuItems = AdminMenuHelper::getMenuStructure($currentPage, []);
$searchSuggestions = NavigationHelper::getSearchSuggestions($menuItems);
?>

<nav class="layout-navbar container-xxl navbar navbar-expand-xl navbar-detached align-items-center bg-navbar-theme" id="layout-navbar">
    <div class="layout-menu-toggle navbar-nav align-items-xl-center me-3 me-xl-0 d-xl-none">
        <a class="nav-item nav-link px-0 me-xl-4" href="javascript:void(0)" id="menu-toggle">
            <i class="ri-menu-line"></i>
        </a>
    </div>

    <div class="navbar-nav-right d-flex align-items-center" id="navbar-collapse">
        <!-- Enhanced Search -->
        <div class="navbar-nav align-items-center me-3">
            <div class="nav-item d-flex align-items-center position-relative">
                <i class="ri-search-line fs-4 lh-0 me-2"></i>
                <input type="text"
                       class="form-control border-0 shadow-none admin-search"
                       placeholder="Search pages, users, courses..."
                       aria-label="Search..."
                       id="admin-search-input"
                       autocomplete="off" />

                <!-- Search Results Dropdown -->
                <div class="search-results-dropdown" id="search-results" style="display: none;">
                    <div class="search-results-header">
                        <small class="text-muted">Search Results</small>
                    </div>
                    <div class="search-results-body" id="search-results-body">
                        <!-- Results will be populated by JavaScript -->
                    </div>
                    <div class="search-results-footer">
                        <small class="text-muted">Press Enter to search all</small>
                    </div>
                </div>
            </div>
        </div>
        <!-- /Enhanced Search -->

        <!-- Quick Actions -->
        <?php if (!empty($quickActions)): ?>
        <div class="navbar-nav align-items-center me-3">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle hide-arrow" href="javascript:void(0);" data-bs-toggle="dropdown">
                    <i class="ri-add-circle-line fs-4"></i>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><h6 class="dropdown-header">Quick Actions</h6></li>
                    <?php foreach ($quickActions as $action): ?>
                    <li>
                        <a class="dropdown-item" href="<?= base_url($action['url']) ?>">
                            <i class="<?= $action['icon'] ?> me-2"></i>
                            <?= esc($action['title']) ?>
                        </a>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
        <?php endif; ?>

        <!-- Notifications -->
        <div class="navbar-nav align-items-center me-3">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle hide-arrow" href="javascript:void(0);" data-bs-toggle="dropdown">
                    <i class="ri-notification-line fs-4"></i>
                    <span class="badge bg-danger rounded-pill badge-notifications">3</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><h6 class="dropdown-header">Notifications</h6></li>
                    <li>
                        <a class="dropdown-item" href="#">
                            <div class="d-flex">
                                <div class="flex-shrink-0 me-3">
                                    <div class="avatar avatar-online">
                                        <span class="avatar-initial rounded-circle bg-primary">
                                            <i class="ri-user-line"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">New user registered</h6>
                                    <p class="mb-0">John Doe just signed up</p>
                                    <small class="text-muted">5 minutes ago</small>
                                </div>
                            </div>
                        </a>
                    </li>
                    <li>
                        <div class="dropdown-divider"></div>
                    </li>
                    <li>
                        <a class="dropdown-item text-center" href="<?= base_url('admin/notifications') ?>">
                            View all notifications
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <ul class="navbar-nav flex-row align-items-center ms-auto">
            <!-- Place this tag where you want the button to render. -->
            <li class="nav-item lh-1 me-3">
                <a class="github-button" href="https://github.com/themeselection/sneat-html-admin-template-free" data-icon="octicon-star" data-size="large" data-show-count="true" aria-label="Star themeselection/sneat-html-admin-template-free on GitHub">Star</a>
            </li>

            <!-- User -->
            <li class="nav-item navbar-dropdown dropdown-user dropdown">
                <a class="nav-link dropdown-toggle hide-arrow" href="javascript:void(0);" data-bs-toggle="dropdown">
                    <div class="avatar avatar-online">
                        <img src="<?= base_url('admin/assets/img/avatars/1.png') ?>" alt class="w-px-40 h-auto rounded-circle" />
                    </div>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a class="dropdown-item" href="#">
                            <div class="d-flex">
                                <div class="flex-shrink-0 me-3">
                                    <div class="avatar avatar-online">
                                        <img src="<?= base_url('admin/assets/img/avatars/1.png') ?>" alt class="w-px-40 h-auto rounded-circle" />
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <span class="fw-semibold d-block"><?= session()->get('admin_name') ?? 'Admin User' ?></span>
                                    <small class="text-muted"><?= session()->get('admin_email') ?? '<EMAIL>' ?></small>
                                </div>
                            </div>
                        </a>
                    </li>
                    <li>
                        <div class="dropdown-divider"></div>
                    </li>
                    <li>
                        <a class="dropdown-item" href="<?= base_url('admin/profile') ?>">
                            <i class="ri-user-line me-2"></i>
                            <span class="align-middle">My Profile</span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="<?= base_url('admin/settings') ?>">
                            <i class="ri-settings-3-line me-2"></i>
                            <span class="align-middle">Settings</span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="#">
                            <span class="d-flex align-items-center align-middle">
                                <i class="flex-shrink-0 ri-bank-card-line me-2"></i>
                                <span class="flex-grow-1 align-middle">Billing</span>
                                <span class="flex-shrink-0 badge badge-center rounded-pill bg-danger w-px-20 h-px-20">4</span>
                            </span>
                        </a>
                    </li>
                    <li>
                        <div class="dropdown-divider"></div>
                    </li>
                    <li>
                        <a class="dropdown-item" href="<?= base_url('admin/logout') ?>" onclick="return confirm('Are you sure you want to logout?')">
                            <i class="ri-logout-box-line me-2"></i>
                            <span class="align-middle">Log Out</span>
                        </a>
                    </li>
                </ul>
            </li>
            <!--/ User -->
        </ul>
    </div>
</nav>
