<?php
/**
 * Create test user for API testing
 */

require_once 'vendor/autoload.php';

// Load CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

// Get database connection
$db = \Config\Database::connect();

echo "🔧 Creating test user for API testing...\n";
echo str_repeat("=", 50) . "\n";

try {
    // Check if users table exists
    if (!$db->tableExists('users')) {
        echo "❌ Users table does not exist\n";
        echo "💡 Please run database migrations first\n";
        exit(1);
    }

    // Check if test user already exists
    $existingUser = $db->table('users')
                      ->where('username', 'admin')
                      ->orWhere('email', '<EMAIL>')
                      ->get()
                      ->getRowArray();

    if ($existingUser) {
        echo "✅ Test user already exists\n";
        echo "Username: admin\n";
        echo "Email: <EMAIL>\n";
        echo "Password: admin123\n\n";
        exit(0);
    }

    // Create test user
    $userData = [
        'username' => 'admin',
        'email' => '<EMAIL>',
        'password_hash' => password_hash('admin123', PASSWORD_DEFAULT),
        'full_name' => 'Administrator',
        'role_id' => 1,
        'nip' => '123456789',
        'is_active' => 1,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];

    $result = $db->table('users')->insert($userData);

    if ($result) {
        echo "✅ Test user created successfully!\n";
        echo "Username: admin\n";
        echo "Email: <EMAIL>\n";
        echo "Password: admin123\n";
        echo "Role ID: 1\n\n";
        echo "🔑 You can now test the API login endpoint\n";
    } else {
        echo "❌ Failed to create test user\n";
        echo "Error: " . $db->error()['message'] . "\n";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "💡 Make sure database is properly configured and accessible\n";
}
?>
