<?php

namespace App\Controllers;

use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

/**
 * Admin Dashboard Controller
 * 
 * Handles all admin dashboard functionality using Materio template
 */
class AdminController extends BaseController
{
    protected $session;
    protected $userModel;

    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);

        $this->session = \Config\Services::session();
        $this->userModel = new \App\Models\UserModel();

        // Get current route
        $router = service('router');
        $currentMethod = $router->methodName();

        // Skip authentication for login-related methods
        $publicMethods = ['login', 'processLogin'];

        // Check if user is authenticated and has admin role (except for public methods)
        if (!in_array($currentMethod, $publicMethods) && !$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }
    }

    /**
     * Admin Dashboard Home
     */
    public function index()
    {
        $data = [
            'title' => 'Dashboard',
            'subtitle' => 'Overview of your RPS system performance and statistics',
            'page' => 'dashboard',
            'stats' => $this->getDashboardStats(),
            'recent_activities' => $this->getRecentActivities(),
            'charts_data' => $this->getChartsData()
        ];

        return view('admin/dashboard/index', $data);
    }

    /**
     * Admin Login Page
     */
    public function login()
    {
        // If already logged in, redirect to dashboard
        if ($this->isAdminAuthenticated()) {
            return redirect()->to('/admin');
        }

        $data = [
            'title' => 'Admin Login',
            'page' => 'login'
        ];

        return view('admin/auth/login', $data);
    }

    /**
     * Process Admin Login
     */
    public function processLogin()
    {
        $rules = [
            'email' => 'required|valid_email',
            'password' => 'required|min_length[6]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');

        $user = $this->userModel->where('email', $email)->first();

        if ($user && password_verify($password, $user['password'])) {
            // Check if user has admin role
            if ($user['role_id'] == 1) { // Assuming role_id 1 is admin
                $this->session->set([
                    'admin_id' => $user['id'],
                    'admin_email' => $user['email'],
                    'admin_name' => $user['name'],
                    'admin_logged_in' => true
                ]);

                return redirect()->to('/admin')->with('success', 'Login successful');
            } else {
                return redirect()->back()->with('error', 'Access denied. Admin privileges required.');
            }
        }

        return redirect()->back()->with('error', 'Invalid credentials');
    }

    /**
     * Admin Logout
     */
    public function logout()
    {
        $this->session->destroy();
        return redirect()->to('/admin/login')->with('success', 'Logged out successfully');
    }

    /**
     * Users Management
     */
    public function users()
    {
        $data = [
            'title' => 'Users Management',
            'subtitle' => 'Manage system users, roles, and permissions',
            'page' => 'users',
            'users' => $this->userModel->findAll()
        ];

        return view('admin/users/index', $data);
    }

    /**
     * Courses Management
     */
    public function courses()
    {
        // $courseModel = new \App\Models\CourseModel();

        $data = [
            'title' => 'Courses Management',
            'page' => 'courses',
            'courses' => [] // $courseModel->findAll()
        ];

        return view('admin/courses/index', $data);
    }

    /**
     * CPMK Management
     */
    public function cpmk()
    {
        // $cpmkModel = new \App\Models\CpmkModel();

        $data = [
            'title' => 'CPMK Management',
            'page' => 'cpmk',
            'cpmk_list' => [] // $cpmkModel->findAll()
        ];

        return view('admin/cpmk/index', $data);
    }

    /**
     * CPL Management
     */
    public function cpl()
    {
        // $cplModel = new \App\Models\CplModel();

        $data = [
            'title' => 'CPL Management',
            'page' => 'cpl',
            'cpl_list' => [] // $cplModel->findAll()
        ];

        return view('admin/cpl/index', $data);
    }

    /**
     * Reports
     */
    public function reports()
    {
        $data = [
            'title' => 'Reports',
            'page' => 'reports',
            'report_data' => $this->getReportData()
        ];

        return view('admin/reports/index', $data);
    }

    /**
     * Settings
     */
    public function settings()
    {
        $data = [
            'title' => 'Settings',
            'page' => 'settings'
        ];

        return view('admin/settings/index', $data);
    }

    /**
     * Check if admin is authenticated
     */
    private function isAdminAuthenticated()
    {
        return $this->session->get('admin_logged_in') === true;
    }

    /**
     * Get dashboard statistics
     */
    private function getDashboardStats()
    {
        $userModel = new \App\Models\UserModel();
        // $courseModel = new \App\Models\CourseModel();
        // $cpmkModel = new \App\Models\CpmkModel();
        // $cplModel = new \App\Models\CplModel();

        return [
            'total_users' => $userModel->countAll(),
            'total_courses' => 0, // $courseModel->countAll(),
            'total_cpmk' => 0, // $cpmkModel->countAll(),
            'total_cpl' => 0 // $cplModel->countAll()
        ];
    }

    /**
     * Get recent activities
     */
    private function getRecentActivities()
    {
        // This would typically come from an activity log table
        return [
            [
                'action' => 'User created',
                'description' => 'New user John Doe was created',
                'timestamp' => date('Y-m-d H:i:s', strtotime('-2 hours')),
                'type' => 'user'
            ],
            [
                'action' => 'Course updated',
                'description' => 'Course "Web Programming" was updated',
                'timestamp' => date('Y-m-d H:i:s', strtotime('-4 hours')),
                'type' => 'course'
            ],
            [
                'action' => 'CPMK added',
                'description' => 'New CPMK was added to course',
                'timestamp' => date('Y-m-d H:i:s', strtotime('-6 hours')),
                'type' => 'cpmk'
            ]
        ];
    }

    /**
     * Get charts data for dashboard
     */
    private function getChartsData()
    {
        return [
            'monthly_users' => [
                'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                'data' => [10, 15, 12, 18, 22, 25]
            ],
            'course_completion' => [
                'labels' => ['Completed', 'In Progress', 'Not Started'],
                'data' => [65, 25, 10]
            ]
        ];
    }

    /**
     * Get report data
     */
    private function getReportData()
    {
        return [
            'cpmk_achievement' => [
                'total' => 150,
                'achieved' => 120,
                'percentage' => 80
            ],
            'cpl_mapping' => [
                'total' => 50,
                'mapped' => 45,
                'percentage' => 90
            ]
        ];
    }
}
