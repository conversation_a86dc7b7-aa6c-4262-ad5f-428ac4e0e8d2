<?php

namespace App\Helpers;

/**
 * Navigation Helper
 * 
 * Provides utilities for navigation management, active states,
 * and menu-related functionality
 */
class NavigationHelper
{
    /**
     * Check if a menu item is active
     * 
     * @param string $menuPage Menu page identifier
     * @param string $currentPage Current page identifier
     * @param array $subPages Optional sub-pages that should also be active
     * @return bool
     */
    public static function isActive($menuPage, $currentPage, $subPages = [])
    {
        if ($menuPage === $currentPage) {
            return true;
        }

        // Check if current page is in sub-pages
        return in_array($currentPage, $subPages);
    }

    /**
     * Get CSS class for active menu items
     * 
     * @param string $menuPage Menu page identifier
     * @param string $currentPage Current page identifier
     * @param array $subPages Optional sub-pages
     * @param string $activeClass CSS class for active state
     * @return string
     */
    public static function getActiveClass($menuPage, $currentPage, $subPages = [], $activeClass = 'active')
    {
        return self::isActive($menuPage, $currentPage, $subPages) ? $activeClass : '';
    }

    /**
     * Generate menu item HTML
     * 
     * @param array $item Menu item configuration
     * @param string $currentPage Current page identifier
     * @return string HTML for menu item
     */
    public static function generateMenuItem($item, $currentPage)
    {
        $isActive = self::isActive($item['page'] ?? '', $currentPage, $item['subPages'] ?? []);
        $hasChildren = !empty($item['children']);
        
        $html = '<li class="menu-item';
        if ($isActive) $html .= ' active';
        if ($hasChildren) $html .= ' menu-item-submenu';
        $html .= '">';

        // Menu link
        $url = $hasChildren ? 'javascript:void(0);' : base_url($item['url']);
        $html .= '<a href="' . $url . '" class="menu-link';
        if ($hasChildren) $html .= ' menu-toggle';
        $html .= '"';
        
        if (isset($item['onclick'])) {
            $html .= ' onclick="' . $item['onclick'] . '"';
        }
        
        $html .= '>';
        $html .= '<i class="menu-icon ' . $item['icon'] . '"></i>';
        $html .= '<div data-i18n="' . $item['title'] . '">' . esc($item['title']) . '</div>';
        
        if (isset($item['badge']) && $item['badge']) {
            $html .= '<div class="badge bg-danger rounded-pill ms-auto">' . esc($item['badge']) . '</div>';
        }
        
        if ($hasChildren) {
            $html .= '<div class="menu-arrow"><i class="ri-arrow-right-s-line"></i></div>';
        }
        
        $html .= '</a>';

        // Submenu
        if ($hasChildren) {
            $html .= '<ul class="menu-sub">';
            foreach ($item['children'] as $child) {
                $html .= '<li class="menu-item">';
                $html .= '<a href="' . base_url($child['url']) . '" class="menu-link">';
                $html .= '<div data-i18n="' . $child['title'] . '">' . esc($child['title']) . '</div>';
                $html .= '</a>';
                $html .= '</li>';
            }
            $html .= '</ul>';
        }

        $html .= '</li>';
        
        return $html;
    }

    /**
     * Get page title based on current page
     * 
     * @param string $currentPage Current page identifier
     * @return string Page title
     */
    public static function getPageTitle($currentPage)
    {
        $titles = [
            'dashboard' => 'Dashboard',
            'users' => 'Users Management',
            'users-create' => 'Add New User',
            'users-edit' => 'Edit User',
            'roles' => 'Roles & Permissions',
            'courses' => 'Courses Management',
            'courses-create' => 'Add New Course',
            'courses-edit' => 'Edit Course',
            'cpmk' => 'CPMK Management',
            'cpmk-create' => 'Add New CPMK',
            'cpmk-edit' => 'Edit CPMK',
            'cpl' => 'CPL Management',
            'cpl-create' => 'Add New CPL',
            'cpl-edit' => 'Edit CPL',
            'rps-templates' => 'RPS Templates',
            'materials' => 'Learning Materials',
            'assessments' => 'Assessments',
            'reports' => 'Reports & Analytics',
            'analytics' => 'Analytics Dashboard',
            'settings' => 'System Settings',
            'logs' => 'System Logs'
        ];

        return $titles[$currentPage] ?? 'Admin Dashboard';
    }

    /**
     * Get page description based on current page
     * 
     * @param string $currentPage Current page identifier
     * @return string Page description
     */
    public static function getPageDescription($currentPage)
    {
        $descriptions = [
            'dashboard' => 'Overview of your RPS system performance and statistics',
            'users' => 'Manage system users, roles, and permissions',
            'courses' => 'Manage academic courses and curriculum',
            'cpmk' => 'Manage Course Learning Outcomes (Capaian Pembelajaran Mata Kuliah)',
            'cpl' => 'Manage Program Learning Outcomes (Capaian Pembelajaran Lulusan)',
            'reports' => 'Generate and view detailed system reports',
            'settings' => 'Configure system settings and preferences'
        ];

        return $descriptions[$currentPage] ?? 'Manage your RPS system efficiently';
    }

    /**
     * Get navigation statistics
     * 
     * @return array Navigation statistics
     */
    public static function getNavigationStats()
    {
        return [
            'total_pages' => 20,
            'protected_pages' => 18,
            'public_pages' => 2,
            'menu_sections' => 6,
            'submenu_items' => 15
        ];
    }

    /**
     * Generate quick actions menu
     * 
     * @param string $currentPage Current page identifier
     * @return array Quick actions for current page
     */
    public static function getQuickActions($currentPage)
    {
        $actions = [
            'dashboard' => [
                [
                    'title' => 'Add User',
                    'icon' => 'ri-user-add-line',
                    'url' => 'admin/users/create',
                    'class' => 'btn-primary'
                ],
                [
                    'title' => 'Add Course',
                    'icon' => 'ri-book-add-line',
                    'url' => 'admin/courses/create',
                    'class' => 'btn-success'
                ],
                [
                    'title' => 'View Reports',
                    'icon' => 'ri-bar-chart-line',
                    'url' => 'admin/reports',
                    'class' => 'btn-info'
                ]
            ],
            'users' => [
                [
                    'title' => 'Add User',
                    'icon' => 'ri-user-add-line',
                    'url' => 'admin/users/create',
                    'class' => 'btn-primary'
                ],
                [
                    'title' => 'Import Users',
                    'icon' => 'ri-upload-line',
                    'url' => 'admin/users/import',
                    'class' => 'btn-secondary'
                ],
                [
                    'title' => 'Export Users',
                    'icon' => 'ri-download-line',
                    'url' => 'admin/users/export',
                    'class' => 'btn-outline-primary'
                ]
            ],
            'courses' => [
                [
                    'title' => 'Add Course',
                    'icon' => 'ri-book-add-line',
                    'url' => 'admin/courses/create',
                    'class' => 'btn-primary'
                ],
                [
                    'title' => 'Import Courses',
                    'icon' => 'ri-upload-line',
                    'url' => 'admin/courses/import',
                    'class' => 'btn-secondary'
                ],
                [
                    'title' => 'Course Templates',
                    'icon' => 'ri-file-copy-line',
                    'url' => 'admin/courses/templates',
                    'class' => 'btn-outline-success'
                ]
            ]
        ];

        return $actions[$currentPage] ?? [];
    }

    /**
     * Check if user has permission for a menu item
     * 
     * @param string $permission Permission string
     * @param array $userPermissions User's permissions
     * @return bool
     */
    public static function hasPermission($permission, $userPermissions)
    {
        if (!$permission) {
            return true; // No permission required
        }

        return in_array($permission, $userPermissions);
    }

    /**
     * Get menu item count by section
     * 
     * @param array $menuItems Menu structure
     * @return array Section counts
     */
    public static function getMenuSectionCounts($menuItems)
    {
        $counts = [];
        $currentSection = null;

        foreach ($menuItems as $item) {
            if ($item['type'] === 'header') {
                $currentSection = $item['title'];
                $counts[$currentSection] = 0;
            } elseif ($item['type'] === 'item' && $currentSection) {
                $counts[$currentSection]++;
            }
        }

        return $counts;
    }

    /**
     * Generate mobile menu toggle button
     * 
     * @return string HTML for mobile menu toggle
     */
    public static function getMobileMenuToggle()
    {
        return '<button type="button" class="btn btn-sm btn-outline-secondary d-lg-none" id="mobile-menu-toggle">
                    <i class="ri-menu-line"></i>
                    <span class="ms-1">Menu</span>
                </button>';
    }

    /**
     * Get search suggestions based on menu items
     * 
     * @param array $menuItems Menu structure
     * @return array Search suggestions
     */
    public static function getSearchSuggestions($menuItems)
    {
        $suggestions = [];

        foreach ($menuItems as $item) {
            if ($item['type'] === 'item') {
                $suggestions[] = [
                    'title' => $item['title'],
                    'url' => $item['url'],
                    'icon' => $item['icon'],
                    'keywords' => strtolower($item['title'])
                ];

                // Add children to suggestions
                if (!empty($item['children'])) {
                    foreach ($item['children'] as $child) {
                        $suggestions[] = [
                            'title' => $child['title'],
                            'url' => $child['url'],
                            'icon' => $item['icon'],
                            'keywords' => strtolower($child['title'] . ' ' . $item['title'])
                        ];
                    }
                }
            }
        }

        return $suggestions;
    }
}
