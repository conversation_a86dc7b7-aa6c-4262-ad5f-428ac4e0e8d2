<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('page_css') ?>
<style>
.stats-card {
    background: linear-gradient(135deg, var(--v-theme-primary) 0%, #5f61e6 100%);
    color: white;
    border: none;
    border-radius: 0.5rem;
}

.stats-card .card-body {
    padding: 2rem;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

.chart-container {
    position: relative;
    height: 300px;
}

.activity-item {
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.activity-icon.user {
    background: rgba(113, 221, 55, 0.1);
    color: #71dd37;
}

.activity-icon.course {
    background: rgba(105, 108, 255, 0.1);
    color: #696cff;
}

.activity-icon.cpmk {
    background: rgba(3, 195, 236, 0.1);
    color: #03c3ec;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div id="admin-dashboard" class="row">
    <!-- Statistics Cards -->
    <div class="col-lg-3 col-md-6 col-12 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number"><?= number_format($stats['total_users']) ?></div>
                        <div class="stats-label">Total Users</div>
                    </div>
                    <div class="stats-icon">
                        <i class="ri-user-line" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-12 mb-4">
        <div class="card" style="background: linear-gradient(135deg, #71dd37 0%, #5cb85c 100%); color: white; border: none; border-radius: 0.5rem;">
            <div class="card-body" style="padding: 2rem;">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number"><?= number_format($stats['total_courses']) ?></div>
                        <div class="stats-label">Total Courses</div>
                    </div>
                    <div class="stats-icon">
                        <i class="ri-book-open-line" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-12 mb-4">
        <div class="card" style="background: linear-gradient(135deg, #03c3ec 0%, #0288d1 100%); color: white; border: none; border-radius: 0.5rem;">
            <div class="card-body" style="padding: 2rem;">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number"><?= number_format($stats['total_cpmk']) ?></div>
                        <div class="stats-label">Total CPMK</div>
                    </div>
                    <div class="stats-icon">
                        <i class="ri-target-line" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 col-12 mb-4">
        <div class="card" style="background: linear-gradient(135deg, #ffab00 0%, #ff9800 100%); color: white; border: none; border-radius: 0.5rem;">
            <div class="card-body" style="padding: 2rem;">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number"><?= number_format($stats['total_cpl']) ?></div>
                        <div class="stats-label">Total CPL</div>
                    </div>
                    <div class="stats-icon">
                        <i class="ri-focus-3-line" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="col-lg-8 col-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Monthly User Registration</h5>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="ri-more-2-line"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">View Details</a></li>
                        <li><a class="dropdown-item" href="#">Export Data</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="monthlyUsersChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Course Completion Status</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="courseCompletionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Recent Activities</h5>
                <a href="#" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body p-0">
                <?php foreach ($recent_activities as $activity): ?>
                <div class="activity-item d-flex align-items-center">
                    <div class="activity-icon <?= $activity['type'] ?>">
                        <?php if ($activity['type'] == 'user'): ?>
                            <i class="ri-user-line"></i>
                        <?php elseif ($activity['type'] == 'course'): ?>
                            <i class="ri-book-open-line"></i>
                        <?php else: ?>
                            <i class="ri-target-line"></i>
                        <?php endif; ?>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1"><?= esc($activity['action']) ?></h6>
                        <p class="mb-1 text-muted"><?= esc($activity['description']) ?></p>
                        <small class="text-muted"><?= date('M d, Y H:i', strtotime($activity['timestamp'])) ?></small>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 col-12 mb-3">
                        <a href="<?= base_url('admin/users') ?>" class="btn btn-outline-primary w-100 d-flex align-items-center justify-content-center" style="height: 60px;">
                            <i class="ri-user-add-line me-2"></i>
                            Manage Users
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 col-12 mb-3">
                        <a href="<?= base_url('admin/courses') ?>" class="btn btn-outline-success w-100 d-flex align-items-center justify-content-center" style="height: 60px;">
                            <i class="ri-book-add-line me-2"></i>
                            Add Course
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 col-12 mb-3">
                        <a href="<?= base_url('admin/cpmk') ?>" class="btn btn-outline-info w-100 d-flex align-items-center justify-content-center" style="height: 60px;">
                            <i class="ri-target-line me-2"></i>
                            Manage CPMK
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 col-12 mb-3">
                        <a href="<?= base_url('admin/reports') ?>" class="btn btn-outline-warning w-100 d-flex align-items-center justify-content-center" style="height: 60px;">
                            <i class="ri-bar-chart-line me-2"></i>
                            View Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('page_js') ?>
<script>
// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    // Monthly Users Chart
    const monthlyUsersCtx = document.getElementById('monthlyUsersChart');
    if (monthlyUsersCtx) {
        new Chart(monthlyUsersCtx, {
            type: 'line',
            data: {
                labels: <?= json_encode($charts_data['monthly_users']['labels']) ?>,
                datasets: [{
                    label: 'New Users',
                    data: <?= json_encode($charts_data['monthly_users']['data']) ?>,
                    borderColor: 'rgb(105, 108, 255)',
                    backgroundColor: 'rgba(105, 108, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // Course Completion Chart
    const courseCompletionCtx = document.getElementById('courseCompletionChart');
    if (courseCompletionCtx) {
        new Chart(courseCompletionCtx, {
            type: 'doughnut',
            data: {
                labels: <?= json_encode($charts_data['course_completion']['labels']) ?>,
                datasets: [{
                    data: <?= json_encode($charts_data['course_completion']['data']) ?>,
                    backgroundColor: [
                        'rgb(113, 221, 55)',
                        'rgb(255, 171, 0)',
                        'rgb(255, 62, 29)'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}
</script>
<?= $this->endSection() ?>
