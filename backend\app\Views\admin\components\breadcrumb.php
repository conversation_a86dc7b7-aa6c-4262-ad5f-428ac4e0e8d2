<?php
// Load menu helper for breadcrumb
use App\Helpers\AdminMenuHelper;

// Get current page from data or default to dashboard
$currentPage = $page ?? 'dashboard';

// Get breadcrumb items
$breadcrumbItems = AdminMenuHelper::getBreadcrumb($currentPage);
?>

<nav aria-label="breadcrumb" class="breadcrumb-nav">
    <ol class="breadcrumb">
        <li class="breadcrumb-item">
            <a href="<?= base_url('admin') ?>" class="breadcrumb-link">
                <i class="ri-home-line me-1"></i>
                Home
            </a>
        </li>
        
        <?php foreach ($breadcrumbItems as $index => $item): ?>
            <?php if ($index === count($breadcrumbItems) - 1): ?>
                <!-- Last item (current page) -->
                <li class="breadcrumb-item active" aria-current="page">
                    <?= esc($item['title']) ?>
                </li>
            <?php else: ?>
                <!-- Intermediate items -->
                <li class="breadcrumb-item">
                    <a href="<?= base_url($item['url']) ?>" class="breadcrumb-link">
                        <?= esc($item['title']) ?>
                    </a>
                </li>
            <?php endif; ?>
        <?php endforeach; ?>
    </ol>
</nav>

<style>
.breadcrumb-nav {
    background: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    margin-bottom: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
}

.breadcrumb-item {
    font-size: 0.875rem;
}

.breadcrumb-link {
    color: #697a8d;
    text-decoration: none;
    transition: color 0.2s ease;
}

.breadcrumb-link:hover {
    color: #696cff;
}

.breadcrumb-item.active {
    color: #5d596c;
    font-weight: 500;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #a8b1bb;
    font-weight: 600;
    margin: 0 0.5rem;
}

/* Responsive breadcrumb */
@media (max-width: 768px) {
    .breadcrumb {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        margin: 0 0.25rem;
    }
}

/* Animation */
.breadcrumb-nav {
    animation: slideInDown 0.3s ease;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
