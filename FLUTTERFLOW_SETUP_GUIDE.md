# 📱 FlutterFlow Integration Setup Guide

## 🎯 Tujuan
Panduan ini membantu Anda mengintegrasikan FlutterFlow.io dengan backend API RPS Web ID untuk development dan production.

## 📋 Prerequisites
- ✅ Backend CodeIgniter 4 sudah running
- ✅ Database sudah dikonfigurasi
- ✅ Composer dependencies sudah terinstall
- ✅ FlutterFlow.io account

## 🚀 Quick Start untuk Development

### 1. Start Backend Server
```bash
cd backend
php start_dev_server.php
```

Atau manual:
```bash
cd backend
php spark serve --host=0.0.0.0 --port=8080
```

### 2. Verifikasi API
```bash
cd backend
php verify_api_for_flutterflow.php
```

### 3. Konfigurasi FlutterFlow

#### API Base URL (Development)
```
http://localhost:8080/api/v1
```

#### API Base URL (Production)
```
https://yourdomain.com/api/v1
```

## 🔧 Konfigurasi FlutterFlow

### 1. API Configuration
Di FlutterFlow, buat API Group dengan konfigurasi:

**Base URL**: `http://localhost:8080/api/v1` (development)
**Headers**:
- `Content-Type: application/json`
- `Accept: application/json`

### 2. Authentication Setup
**Type**: Bearer Token
**Header**: `Authorization: Bearer {token}`

### 3. API Endpoints yang Tersedia

#### Authentication
- **POST** `/auth/login` - User login
- **POST** `/auth/logout` - User logout  
- **POST** `/auth/refresh` - Refresh token
- **GET** `/auth/profile` - Get user profile

#### Users Management
- **GET** `/users` - List users
- **GET** `/users/{id}` - Get user by ID
- **POST** `/users` - Create user
- **PUT** `/users/{id}` - Update user
- **DELETE** `/users/{id}` - Delete user

#### Master Data
- **GET** `/faculties` - List faculties
- **GET** `/faculties/{id}` - Get faculty by ID
- **POST** `/faculties` - Create faculty
- **PUT** `/faculties/{id}` - Update faculty
- **DELETE** `/faculties/{id}` - Delete faculty

- **GET** `/study-programs` - List study programs
- **GET** `/study-programs/{id}` - Get study program by ID
- **POST** `/study-programs` - Create study program
- **PUT** `/study-programs/{id}` - Update study program
- **DELETE** `/study-programs/{id}` - Delete study program

#### Dashboard
- **GET** `/dashboard/stats` - Get dashboard statistics

## 🔑 Sample API Calls

### 1. Login
```json
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

**Response**:
```json
{
  "status": "success",
  "message": "Login successful",
  "data": {
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "full_name": "Administrator",
      "role_id": 1
    },
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 86400
  }
}
```

### 2. Get Profile (Authenticated)
```json
GET /api/v1/auth/profile
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

### 3. Get Users (Authenticated)
```json
GET /api/v1/users?page=1&per_page=10
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## 🌐 CORS Configuration
CORS sudah dikonfigurasi untuk mendukung FlutterFlow:
- ✅ `Access-Control-Allow-Origin: *`
- ✅ `Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS`
- ✅ `Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With`

## 🔒 Security Features
- ✅ JWT Authentication
- ✅ Password hashing
- ✅ Input validation
- ✅ SQL injection protection
- ✅ XSS protection

## 📊 Response Format
Semua API menggunakan format response yang konsisten:

**Success Response**:
```json
{
  "status": "success",
  "message": "Operation successful",
  "code": 200,
  "data": { ... }
}
```

**Error Response**:
```json
{
  "status": "error",
  "message": "Error description",
  "code": 400,
  "errors": { ... }
}
```

## 🛠️ Troubleshooting

### Common Issues:

1. **CORS Error**
   - Pastikan server backend running
   - Periksa konfigurasi CORS di `app/Config/Filters.php`

2. **401 Unauthorized**
   - Periksa JWT token
   - Pastikan header Authorization benar

3. **500 Internal Server Error**
   - Periksa database connection
   - Lihat log error di `writable/logs/`

4. **Database Error**
   - Jalankan `php setup_database.php`
   - Periksa konfigurasi database di `.env`

### Development vs Production

**Development**:
- URL: `http://localhost:8080/api/v1`
- CORS: Permissive (*)
- Debug: Enabled

**Production**:
- URL: `https://yourdomain.com/api/v1`
- CORS: Restricted to specific domains
- Debug: Disabled
- HTTPS: Required

## ✅ Checklist Setup

- [ ] Backend server running
- [ ] Database configured
- [ ] Test user created
- [ ] API endpoints tested
- [ ] CORS working
- [ ] FlutterFlow API group created
- [ ] Authentication flow tested
- [ ] Sample API calls working

## 📞 Support

Jika mengalami masalah:
1. Jalankan `php verify_api_for_flutterflow.php`
2. Periksa log error di `backend/writable/logs/`
3. Test manual dengan curl atau Postman
4. Periksa konfigurasi database dan environment

---

**Status**: ✅ Ready for FlutterFlow Integration
**Last Updated**: 2025-01-27
