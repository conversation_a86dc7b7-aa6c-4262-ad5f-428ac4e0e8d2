<?php

namespace App\Models;

use CodeIgniter\Model;

class StudyProgramModel extends Model
{
    protected $table = 'study_programs';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'name',
        'code',
        'faculty_id',
        'degree_level',
        'description',
        'is_active'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'name' => 'required|min_length[3]|max_length[100]',
        'code' => 'required|min_length[2]|max_length[10]|is_unique[study_programs.code,id,{id}]',
        'faculty_id' => 'required|integer',
        'degree_level' => 'required|in_list[D3,D4,S1,S2,S3]',
        'description' => 'permit_empty|max_length[500]',
        'is_active' => 'in_list[0,1]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'Study program name is required',
            'min_length' => 'Study program name must be at least 3 characters',
            'max_length' => 'Study program name cannot exceed 100 characters'
        ],
        'code' => [
            'required' => 'Study program code is required',
            'min_length' => 'Study program code must be at least 2 characters',
            'max_length' => 'Study program code cannot exceed 10 characters',
            'is_unique' => 'Study program code already exists'
        ],
        'faculty_id' => [
            'required' => 'Faculty is required',
            'integer' => 'Invalid faculty selected'
        ],
        'degree_level' => [
            'required' => 'Degree level is required',
            'in_list' => 'Invalid degree level selected'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;
    protected $allowCallbacks = true;

    /**
     * Get active study programs
     */
    public function getActiveStudyPrograms()
    {
        return $this->where('is_active', 1)->findAll();
    }

    /**
     * Get study programs by faculty
     */
    public function getByFaculty($facultyId)
    {
        return $this->where('faculty_id', $facultyId)
                   ->where('is_active', 1)
                   ->findAll();
    }

    /**
     * Get study program with faculty information
     */
    public function getStudyProgramWithFaculty($studyProgramId)
    {
        $builder = $this->db->table($this->table);
        $builder->select('study_programs.*, faculties.name as faculty_name, faculties.code as faculty_code');
        $builder->join('faculties', 'faculties.id = study_programs.faculty_id', 'left');
        $builder->where('study_programs.id', $studyProgramId);

        return $builder->get()->getRowArray();
    }

    /**
     * Get study programs with faculty information
     */
    public function getStudyProgramsWithFaculty($limit = null, $offset = null)
    {
        $builder = $this->db->table($this->table);
        $builder->select('study_programs.*, faculties.name as faculty_name, faculties.code as faculty_code');
        $builder->join('faculties', 'faculties.id = study_programs.faculty_id', 'left');
        $builder->where('study_programs.is_active', 1);

        if ($limit !== null) {
            $builder->limit($limit, $offset);
        }

        return $builder->get()->getResultArray();
    }

    /**
     * Get study program statistics
     */
    public function getStudyProgramStats()
    {
        $total = $this->countAllResults();
        $active = $this->where('is_active', 1)->countAllResults();
        $inactive = $this->where('is_active', 0)->countAllResults();

        $degreeStats = $this->select('degree_level, COUNT(*) as count')
                           ->where('is_active', 1)
                           ->groupBy('degree_level')
                           ->findAll();

        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $inactive,
            'by_degree' => $degreeStats
        ];
    }
}
