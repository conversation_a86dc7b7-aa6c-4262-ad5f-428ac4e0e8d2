<?php
/**
 * 🔍 API Verification Script untuk FlutterFlow Integration
 * 
 * Script ini memverifikasi bahwa API backend siap untuk diakses oleh FlutterFlow.io
 * Men<PERSON>ji endpoint, CORS, authentication, dan response format.
 */

echo "🔍 Verifying API for FlutterFlow Integration\n";
echo str_repeat("=", 60) . "\n\n";

// Configuration
$baseUrl = 'http://localhost:8080';
$apiBaseUrl = $baseUrl . '/api/v1';

// Test data for login
$testCredentials = [
    'username' => 'admin',
    'password' => 'admin123'
];

/**
 * Make HTTP request with proper headers
 */
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $defaultHeaders = [
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    $headers = array_merge($defaultHeaders, $headers);
    
    $context = stream_context_create([
        'http' => [
            'method' => $method,
            'header' => implode("\r\n", $headers),
            'content' => $data ? json_encode($data) : null,
            'timeout' => 10,
            'ignore_errors' => true
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    $httpCode = isset($http_response_header[0]) ? $http_response_header[0] : 'No response';
    
    return [
        'response' => $response,
        'http_code' => $httpCode,
        'headers' => $http_response_header ?? []
    ];
}

/**
 * Check if server is running
 */
function checkServerStatus($baseUrl) {
    echo "1. 🖥️  Checking Backend Server Status...\n";
    
    $result = makeRequest($baseUrl);
    
    if ($result['response'] === false) {
        echo "   ❌ Backend server not running\n";
        echo "   💡 Start server with: php spark serve --host=0.0.0.0 --port=8080\n\n";
        return false;
    }
    
    echo "   ✅ Backend server is running\n";
    echo "   📍 URL: $baseUrl\n\n";
    return true;
}

/**
 * Test CORS headers
 */
function testCorsHeaders($apiBaseUrl) {
    echo "2. 🌐 Testing CORS Configuration...\n";
    
    // Test OPTIONS request (preflight)
    $result = makeRequest($apiBaseUrl . '/auth/profile', 'OPTIONS');
    
    $corsHeaders = [
        'Access-Control-Allow-Origin',
        'Access-Control-Allow-Methods',
        'Access-Control-Allow-Headers'
    ];
    
    $corsOk = true;
    foreach ($corsHeaders as $header) {
        $found = false;
        foreach ($result['headers'] as $responseHeader) {
            if (stripos($responseHeader, $header) !== false) {
                echo "   ✅ $header: " . trim(substr($responseHeader, strpos($responseHeader, ':') + 1)) . "\n";
                $found = true;
                break;
            }
        }
        if (!$found) {
            echo "   ❌ Missing header: $header\n";
            $corsOk = false;
        }
    }
    
    if ($corsOk) {
        echo "   ✅ CORS configuration is correct\n\n";
    } else {
        echo "   ⚠️  CORS configuration needs attention\n\n";
    }
    
    return $corsOk;
}

/**
 * Test API endpoints
 */
function testApiEndpoints($apiBaseUrl, $testCredentials) {
    echo "3. 🔌 Testing API Endpoints...\n";
    
    $endpoints = [
        'GET /auth/profile' => [
            'url' => $apiBaseUrl . '/auth/profile',
            'method' => 'GET',
            'expected_codes' => [401], // Should be unauthorized without token
            'description' => 'Profile endpoint (should require auth)'
        ],
        'POST /auth/login' => [
            'url' => $apiBaseUrl . '/auth/login',
            'method' => 'POST',
            'data' => $testCredentials,
            'expected_codes' => [200, 401, 422], // Success, unauthorized, or validation error
            'description' => 'Login endpoint'
        ]
    ];
    
    $token = null;
    
    foreach ($endpoints as $name => $config) {
        echo "   Testing: $name\n";
        
        $headers = [];
        if ($token) {
            $headers[] = "Authorization: Bearer $token";
        }
        
        $result = makeRequest(
            $config['url'], 
            $config['method'], 
            $config['data'] ?? null, 
            $headers
        );
        
        // Extract HTTP status code
        preg_match('/HTTP\/\d\.\d\s+(\d+)/', $result['http_code'], $matches);
        $statusCode = isset($matches[1]) ? (int)$matches[1] : 0;
        
        $isExpected = in_array($statusCode, $config['expected_codes']);
        $status = $isExpected ? "✅" : "❌";
        
        echo "   $status Status: {$result['http_code']}\n";
        echo "   📝 {$config['description']}\n";
        
        // Try to parse response as JSON
        if ($result['response']) {
            $jsonResponse = json_decode($result['response'], true);
            if ($jsonResponse) {
                echo "   📄 Response format: Valid JSON\n";
                if (isset($jsonResponse['status'])) {
                    echo "   📊 API Status: {$jsonResponse['status']}\n";
                }
                
                // Extract token from login response
                if ($name === 'POST /auth/login' && $statusCode === 200 && isset($jsonResponse['data']['token'])) {
                    $token = $jsonResponse['data']['token'];
                    echo "   🔑 JWT Token received\n";
                }
            } else {
                echo "   ⚠️  Response format: Invalid JSON\n";
            }
        }
        
        echo "\n";
    }
    
    return $token;
}

/**
 * Test authenticated endpoints
 */
function testAuthenticatedEndpoints($apiBaseUrl, $token) {
    if (!$token) {
        echo "4. 🔐 Skipping authenticated endpoint tests (no token)\n\n";
        return;
    }
    
    echo "4. 🔐 Testing Authenticated Endpoints...\n";
    
    $endpoints = [
        'GET /auth/profile' => $apiBaseUrl . '/auth/profile',
        'GET /users' => $apiBaseUrl . '/users',
        'GET /faculties' => $apiBaseUrl . '/faculties',
    ];
    
    foreach ($endpoints as $name => $url) {
        echo "   Testing: $name\n";
        
        $result = makeRequest($url, 'GET', null, ["Authorization: Bearer $token"]);
        
        preg_match('/HTTP\/\d\.\d\s+(\d+)/', $result['http_code'], $matches);
        $statusCode = isset($matches[1]) ? (int)$matches[1] : 0;
        
        $status = ($statusCode >= 200 && $statusCode < 300) ? "✅" : "❌";
        echo "   $status Status: {$result['http_code']}\n";
        
        if ($result['response']) {
            $jsonResponse = json_decode($result['response'], true);
            if ($jsonResponse && isset($jsonResponse['status'])) {
                echo "   📊 API Status: {$jsonResponse['status']}\n";
            }
        }
        
        echo "\n";
    }
}

/**
 * Display FlutterFlow configuration
 */
function displayFlutterFlowConfig($baseUrl) {
    echo "5. 📱 FlutterFlow Configuration\n";
    echo str_repeat("-", 40) . "\n";
    echo "Base URL (Development): $baseUrl/api/v1\n";
    echo "Base URL (Production): https://yourdomain.com/api/v1\n";
    echo "Authentication: Bearer Token (JWT)\n";
    echo "Content-Type: application/json\n";
    echo "Accept: application/json\n\n";
    
    echo "📋 Sample API Calls for FlutterFlow:\n";
    echo "• Login: POST /auth/login\n";
    echo "• Profile: GET /auth/profile\n";
    echo "• Users: GET /users\n";
    echo "• Faculties: GET /faculties\n";
    echo "• Courses: GET /courses\n\n";
}

// Run verification
if (!checkServerStatus($baseUrl)) {
    exit(1);
}

testCorsHeaders($apiBaseUrl);
$token = testApiEndpoints($apiBaseUrl, $testCredentials);
testAuthenticatedEndpoints($apiBaseUrl, $token);
displayFlutterFlowConfig($baseUrl);

echo "✅ API Verification Complete!\n";
echo "🚀 Your API is ready for FlutterFlow integration.\n";
?>
