<?php

namespace App\Controllers\Api;

use App\Models\UserModel;

class UserController extends BaseApiController
{
    protected $userModel;

    public function __construct()
    {
        parent::__construct();
        $this->userModel = new UserModel();
    }

    /**
     * Get all users
     */
    public function index()
    {
        try {
            $page = $this->request->getGet('page') ?? 1;
            $perPage = $this->request->getGet('per_page') ?? 10;
            $search = $this->request->getGet('search');
            $role = $this->request->getGet('role');

            $conditions = [];
            if ($role) {
                $conditions['role_id'] = $role;
            }

            $query = $this->userModel;
            
            if ($search) {
                $query = $query->groupStart()
                              ->like('username', $search)
                              ->orLike('email', $search)
                              ->orLike('full_name', $search)
                              ->groupEnd();
            }

            $total = $query->countAllResults(false);
            $users = $query->findAll($perPage, ($page - 1) * $perPage);

            // Remove password hash from response
            foreach ($users as &$user) {
                unset($user['password_hash']);
            }

            $result = [
                'data' => $users,
                'pagination' => [
                    'current_page' => (int)$page,
                    'per_page' => (int)$perPage,
                    'total' => $total,
                    'total_pages' => ceil($total / $perPage),
                    'has_next' => $page < ceil($total / $perPage),
                    'has_prev' => $page > 1
                ]
            ];

            return $this->respondSuccess($result, 'Users retrieved successfully');

        } catch (\Exception $e) {
            return $this->respondError('Failed to retrieve users: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get user by ID
     */
    public function show($id = null)
    {
        try {
            if (!$id) {
                return $this->respondError('User ID is required', 400);
            }

            $user = $this->userModel->find($id);
            
            if (!$user) {
                return $this->respondNotFound('User not found');
            }

            // Remove password hash from response
            unset($user['password_hash']);

            return $this->respondSuccess($user, 'User retrieved successfully');

        } catch (\Exception $e) {
            return $this->respondError('Failed to retrieve user: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Create new user
     */
    public function create()
    {
        try {
            $validation = $this->validateRequest([
                'username' => 'required|min_length[3]|max_length[50]|is_unique[users.username]',
                'email' => 'required|valid_email|is_unique[users.email]',
                'password' => 'required|min_length[6]',
                'full_name' => 'required|min_length[3]|max_length[100]',
                'role_id' => 'required|integer',
                'nip' => 'permit_empty|max_length[20]'
            ]);

            if ($validation) {
                return $validation;
            }

            $data = [
                'username' => $this->request->getPost('username'),
                'email' => $this->request->getPost('email'),
                'password_hash' => password_hash($this->request->getPost('password'), PASSWORD_DEFAULT),
                'full_name' => $this->request->getPost('full_name'),
                'role_id' => $this->request->getPost('role_id'),
                'nip' => $this->request->getPost('nip'),
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $userId = $this->userModel->insert($data);

            if (!$userId) {
                return $this->respondError('Failed to create user', 500);
            }

            $user = $this->userModel->find($userId);
            unset($user['password_hash']);

            return $this->respondSuccess($user, 'User created successfully', 201);

        } catch (\Exception $e) {
            return $this->respondError('Failed to create user: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Update user
     */
    public function update($id = null)
    {
        try {
            if (!$id) {
                return $this->respondError('User ID is required', 400);
            }

            $user = $this->userModel->find($id);
            if (!$user) {
                return $this->respondNotFound('User not found');
            }

            $rules = [
                'username' => "permit_empty|min_length[3]|max_length[50]|is_unique[users.username,id,$id]",
                'email' => "permit_empty|valid_email|is_unique[users.email,id,$id]",
                'full_name' => 'permit_empty|min_length[3]|max_length[100]',
                'role_id' => 'permit_empty|integer',
                'nip' => 'permit_empty|max_length[20]',
                'is_active' => 'permit_empty|in_list[0,1]'
            ];

            $validation = $this->validateRequest($rules);
            if ($validation) {
                return $validation;
            }

            $data = [];
            $fields = ['username', 'email', 'full_name', 'role_id', 'nip', 'is_active'];
            
            foreach ($fields as $field) {
                $value = $this->request->getPost($field);
                if ($value !== null) {
                    $data[$field] = $value;
                }
            }

            // Handle password update
            $password = $this->request->getPost('password');
            if ($password) {
                $data['password_hash'] = password_hash($password, PASSWORD_DEFAULT);
            }

            $data['updated_at'] = date('Y-m-d H:i:s');

            $this->userModel->update($id, $data);

            $updatedUser = $this->userModel->find($id);
            unset($updatedUser['password_hash']);

            return $this->respondSuccess($updatedUser, 'User updated successfully');

        } catch (\Exception $e) {
            return $this->respondError('Failed to update user: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Delete user
     */
    public function delete($id = null)
    {
        try {
            if (!$id) {
                return $this->respondError('User ID is required', 400);
            }

            $user = $this->userModel->find($id);
            if (!$user) {
                return $this->respondNotFound('User not found');
            }

            // Prevent deleting the current user
            $currentUser = $this->getAuthenticatedUser();
            if ($currentUser && $currentUser->user_id == $id) {
                return $this->respondError('Cannot delete your own account', 403);
            }

            $this->userModel->delete($id);

            return $this->respondSuccess(null, 'User deleted successfully');

        } catch (\Exception $e) {
            return $this->respondError('Failed to delete user: ' . $e->getMessage(), 500);
        }
    }
}
