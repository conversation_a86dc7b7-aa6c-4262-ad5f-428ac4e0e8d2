<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */

// Frontend Assets - Serve static files
$routes->get('frontend/assets/(:any)', 'Frontend::assets/$1');
$routes->get('frontend/favicon.ico', function() {
    $frontendDistPath = ROOTPATH . '../frontend/dist/favicon.ico';
    if (file_exists($frontendDistPath)) {
        header('Content-Type: image/x-icon');
        readfile($frontendDistPath);
        exit;
    }
    throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
});

// Frontend Routes - Serve Vue.js application
$routes->get('/', 'Frontend::index');
$routes->get('login', 'Frontend::index');
$routes->get('dashboard', 'Frontend::index');
$routes->get('courses', 'Frontend::index');
$routes->get('courses/(:any)', 'Frontend::index');
$routes->get('cpmk', 'Frontend::index');
$routes->get('cpl', 'Frontend::index');
$routes->get('assessments', 'Frontend::index');
$routes->get('reports', 'Frontend::index');
$routes->get('users', 'Frontend::index');
$routes->get('faculties', 'Frontend::index');
$routes->get('study-programs', 'Frontend::index');
$routes->get('profile', 'Frontend::index');

// Admin Routes
$routes->group('admin', ['namespace' => 'App\Controllers'], function($routes) {
    // Admin Authentication (no auth required)
    $routes->get('login', 'AdminController::login');
    $routes->post('login', 'AdminController::processLogin');
    $routes->get('logout', 'AdminController::logout');

    // Protected Admin Routes (auth required)
    $routes->get('/', 'AdminController::index');
    $routes->get('dashboard', 'AdminController::index');

    // User Management Routes
    $routes->group('users', function($routes) {
        $routes->get('/', 'AdminController::users');
        $routes->get('create', 'AdminController::createUser');
        $routes->post('create', 'AdminController::storeUser');
        $routes->get('edit/(:num)', 'AdminController::editUser/$1');
        $routes->post('edit/(:num)', 'AdminController::updateUser/$1');
        $routes->delete('(:num)', 'AdminController::deleteUser/$1');
        $routes->get('roles', 'AdminController::userRoles');
        $routes->get('import', 'AdminController::importUsers');
        $routes->post('import', 'AdminController::processImportUsers');
        $routes->get('export', 'AdminController::exportUsers');
    });

    // Roles & Permissions Routes
    $routes->group('roles', function($routes) {
        $routes->get('/', 'AdminController::roles');
        $routes->get('create', 'AdminController::createRole');
        $routes->post('create', 'AdminController::storeRole');
        $routes->get('edit/(:num)', 'AdminController::editRole/$1');
        $routes->post('edit/(:num)', 'AdminController::updateRole/$1');
        $routes->delete('(:num)', 'AdminController::deleteRole/$1');
    });

    // Course Management Routes
    $routes->group('courses', function($routes) {
        $routes->get('/', 'AdminController::courses');
        $routes->get('create', 'AdminController::createCourse');
        $routes->post('create', 'AdminController::storeCourse');
        $routes->get('edit/(:num)', 'AdminController::editCourse/$1');
        $routes->post('edit/(:num)', 'AdminController::updateCourse/$1');
        $routes->delete('(:num)', 'AdminController::deleteCourse/$1');
        $routes->get('categories', 'AdminController::courseCategories');
        $routes->get('templates', 'AdminController::courseTemplates');
        $routes->get('import', 'AdminController::importCourses');
        $routes->post('import', 'AdminController::processImportCourses');
        $routes->get('export', 'AdminController::exportCourses');
    });

    // CPMK Management Routes
    $routes->group('cpmk', function($routes) {
        $routes->get('/', 'AdminController::cpmk');
        $routes->get('create', 'AdminController::createCpmk');
        $routes->post('create', 'AdminController::storeCpmk');
        $routes->get('edit/(:num)', 'AdminController::editCpmk/$1');
        $routes->post('edit/(:num)', 'AdminController::updateCpmk/$1');
        $routes->delete('(:num)', 'AdminController::deleteCpmk/$1');
        $routes->get('mapping', 'AdminController::cpmkMapping');
        $routes->post('mapping', 'AdminController::saveCpmkMapping');
    });

    // CPL Management Routes
    $routes->group('cpl', function($routes) {
        $routes->get('/', 'AdminController::cpl');
        $routes->get('create', 'AdminController::createCpl');
        $routes->post('create', 'AdminController::storeCpl');
        $routes->get('edit/(:num)', 'AdminController::editCpl/$1');
        $routes->post('edit/(:num)', 'AdminController::updateCpl/$1');
        $routes->delete('(:num)', 'AdminController::deleteCpl/$1');
        $routes->get('assessment', 'AdminController::cplAssessment');
        $routes->post('assessment', 'AdminController::saveCplAssessment');
    });

    // RPS Templates Routes
    $routes->group('rps-templates', function($routes) {
        $routes->get('/', 'AdminController::rpsTemplates');
        $routes->get('create', 'AdminController::createRpsTemplate');
        $routes->post('create', 'AdminController::storeRpsTemplate');
        $routes->get('edit/(:num)', 'AdminController::editRpsTemplate/$1');
        $routes->post('edit/(:num)', 'AdminController::updateRpsTemplate/$1');
        $routes->delete('(:num)', 'AdminController::deleteRpsTemplate/$1');
    });

    // Content Management Routes
    $routes->get('materials', 'AdminController::materials');
    $routes->get('assessments', 'AdminController::assessments');

    // Reports & Analytics Routes
    $routes->group('reports', function($routes) {
        $routes->get('/', 'AdminController::reports');
        $routes->get('courses', 'AdminController::courseReports');
        $routes->get('users', 'AdminController::userReports');
        $routes->get('cpmk', 'AdminController::cpmkReports');
        $routes->get('cpl', 'AdminController::cplReports');
        $routes->get('export/(:alpha)', 'AdminController::exportReport/$1');
    });

    $routes->get('analytics', 'AdminController::analytics');

    // System Routes
    $routes->group('settings', function($routes) {
        $routes->get('/', 'AdminController::settings');
        $routes->get('general', 'AdminController::generalSettings');
        $routes->post('general', 'AdminController::saveGeneralSettings');
        $routes->get('email', 'AdminController::emailSettings');
        $routes->post('email', 'AdminController::saveEmailSettings');
        $routes->get('backup', 'AdminController::backupSettings');
        $routes->post('backup', 'AdminController::createBackup');
        $routes->post('restore', 'AdminController::restoreBackup');
    });

    $routes->get('logs', 'AdminController::logs');
    $routes->get('notifications', 'AdminController::notifications');

    // API Routes for AJAX calls
    $routes->group('api', function($routes) {
        $routes->post('search', 'AdminController::apiSearch');
        $routes->get('stats', 'AdminController::apiStats');
        $routes->post('quick-action', 'AdminController::apiQuickAction');
    });
});

// API Routes
$routes->group('api/v1', ['namespace' => 'App\Controllers\Api'], function($routes) {
    // Authentication routes
    $routes->post('auth/login', 'AuthController::login');
    $routes->post('auth/logout', 'AuthController::logout');
    $routes->post('auth/refresh', 'AuthController::refresh');
    $routes->get('auth/profile', 'AuthController::profile', ['filter' => 'auth']);

    // Protected routes
    $routes->group('', ['filter' => 'auth'], function($routes) {
        // User management
        $routes->resource('users', ['controller' => 'UserController']);

        // Master data
        $routes->resource('faculties', ['controller' => 'FacultyController']);
        $routes->resource('study-programs', ['controller' => 'StudyProgramController']);

        // Course management
        $routes->resource('courses', ['controller' => 'CourseController']);
        $routes->get('courses/(:num)/references', 'CourseController::getReferences/$1');
        $routes->post('courses/(:num)/references', 'CourseController::addReference/$1');
        $routes->get('courses/(:num)/topics', 'CourseController::getTopics/$1');
        $routes->post('courses/(:num)/topics', 'CourseController::addTopic/$1');

        // CPMK management
        $routes->resource('cpmk', ['controller' => 'CpmkController']);
        $routes->get('cpmk/(:num)/sub-cpmk', 'CpmkController::getSubCpmk/$1');
        $routes->post('cpmk/(:num)/sub-cpmk', 'CpmkController::addSubCpmk/$1');
        $routes->get('cpmk/(:num)/cpl-relations', 'CpmkController::getCplRelations/$1');
        $routes->post('cpmk/(:num)/cpl-relations', 'CpmkController::addCplRelation/$1');

        // CPL management
        $routes->resource('cpl', ['controller' => 'CplController']);

        // Assessment management
        $routes->resource('assessments/methods', ['controller' => 'AssessmentMethodController']);
        $routes->resource('assessments/plans', ['controller' => 'AssessmentPlanController']);

        // Reports and dashboard
        $routes->get('reports/cpmk-achievement', 'ReportController::cpmkAchievement');
        $routes->get('reports/cpl-mapping', 'ReportController::cplMapping');
        $routes->get('reports/dashboard', 'ReportController::dashboard');
        $routes->get('dashboard/stats', 'DashboardController::getStats');
    });
});

// Catch-all route for SPA (Single Page Application)
$routes->get('(:any)', 'Frontend::index');
