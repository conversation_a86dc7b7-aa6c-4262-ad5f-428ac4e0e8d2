<?php
// Load menu helper
use App\Helpers\AdminMenuHelper;

// Get current page from data or default to dashboard
$currentPage = $page ?? 'dashboard';

// Get user permissions (you can modify this based on your auth system)
$userPermissions = [
    'admin.dashboard.view',
    'admin.users.view',
    'admin.users.create',
    'admin.users.roles',
    'admin.roles.view',
    'admin.academic.view',
    'admin.courses.view',
    'admin.courses.create',
    'admin.courses.categories',
    'admin.cpmk.view',
    'admin.cpmk.create',
    'admin.cpmk.mapping',
    'admin.cpl.view',
    'admin.cpl.create',
    'admin.cpl.assessment',
    'admin.rps.view',
    'admin.content.view',
    'admin.materials.view',
    'admin.assessments.view',
    'admin.reports.view',
    'admin.reports.courses',
    'admin.reports.users',
    'admin.reports.cpmk',
    'admin.reports.cpl',
    'admin.analytics.view',
    'admin.system.view',
    'admin.settings.view',
    'admin.settings.general',
    'admin.settings.email',
    'admin.settings.backup',
    'admin.logs.view'
];

// Get menu structure
$menuItems = AdminMenuHelper::getMenuStructure($currentPage, $userPermissions);
?>

<aside id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">
    <!-- App Brand -->
    <div class="app-brand demo">
        <a href="<?= base_url('admin') ?>" class="app-brand-link">
            <span class="app-brand-logo demo">
                <svg width="25" viewBox="0 0 25 42" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <defs>
                        <path d="M13.7918663,0.358365126 L3.39788168,7.44174259 C0.566865006,9.69408886 -0.379795268,12.4788597 0.557900856,15.7960551 C0.68998853,16.2305145 1.09562888,17.7872135 3.12357076,19.2293357 C3.8146334,19.7207684 5.32369333,20.3834223 7.65075054,21.2172976 L7.59773219,21.2525164 L2.63468769,24.5493413 C0.445452254,26.3002124 0.0884951797,28.5083815 1.56381646,31.1738486 C2.83770406,32.8170431 5.20850219,33.2640127 7.09180128,32.5391577 C8.347334,32.0559211 11.4559176,30.0011079 16.4175519,26.3747182 C18.0338572,24.4997857 18.6973423,22.4544883 18.4080071,20.2388261 C17.963753,17.5346866 16.1776345,15.5799961 13.0496516,14.3747546 L10.9194936,13.4715819 L18.6192054,7.984237 L13.7918663,0.358365126 Z" id="path-1"></path>
                        <path d="M5.47320593,6.00457225 C4.05321814,8.216144 4.36334763,10.0722806 6.40359441,11.5729822 C8.61520715,12.571656 10.0999176,13.2171421 10.8577257,13.5094407 L15.5088241,14.433041 L18.6192054,7.984237 C15.5364148,3.11535317 13.9273018,0.573395879 13.7918663,0.358365126 C13.5790555,0.511491653 10.8061687,2.3935607 5.47320593,6.00457225 Z" id="path-3"></path>
                        <path d="M7.50063644,21.2294429 L12.3234468,23.3159332 C14.1688022,24.7579751 14.397098,26.4880487 13.008334,28.506154 C11.6195701,30.5242593 10.3099883,31.790241 9.07958868,32.3040991 C5.78142938,33.4346997 4.13234973,34 4.13234973,34 C4.13234973,34 2.75489982,33.0538207 2.37032616e-14,31.1614621 C-0.55822714,27.8186216 -0.55822714,26.0572515 -4.05231404e-15,25.8773518 C0.83734071,25.6075023 2.77988457,22.8248993 3.3049379,22.52991 C3.65497346,22.3332504 5.05353963,21.8997614 7.50063644,21.2294429 Z" id="path-4"></path>
                        <path d="M20.6,7.13333333 L25.6,13.8 C26.2627417,14.6836556 26.0836556,15.9372583 25.2,16.6 C24.8538077,16.8596443 24.4327404,17 24,17 L14,17 C12.8954305,17 12,16.1045695 12,15 C12,14.5672596 12.1403557,14.1461923 12.4,13.8 L17.4,7.13333333 C18.0627417,6.24967773 19.3163444,6.07059163 20.2,6.73333333 C20.3516113,6.84704183 20.4862915,6.981722 20.6,7.13333333 Z" id="path-5"></path>
                    </defs>
                    <g id="g-app-brand" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g id="Brand-Logo" transform="translate(-27.000000, -15.000000)">
                            <g id="Icon" transform="translate(27.000000, 15.000000)">
                                <g id="Mask" transform="translate(0.000000, 8.000000)">
                                    <mask id="mask-2" fill="white">
                                        <use xlink:href="#path-1"></use>
                                    </mask>
                                    <use fill="#696cff" xlink:href="#path-1"></use>
                                    <g id="Path-3" mask="url(#mask-2)">
                                        <use fill="#696cff" xlink:href="#path-3"></use>
                                        <use fill-opacity="0.2" fill="#FFFFFF" xlink:href="#path-3"></use>
                                    </g>
                                    <g id="Path-4" mask="url(#mask-2)">
                                        <use fill="#696cff" xlink:href="#path-4"></use>
                                        <use fill-opacity="0.2" fill="#FFFFFF" xlink:href="#path-4"></use>
                                    </g>
                                </g>
                                <g id="Triangle" transform="translate(19.000000, 11.000000) rotate(-300.000000) translate(-19.000000, -11.000000) ">
                                    <use fill="#696cff" xlink:href="#path-5"></use>
                                    <use fill-opacity="0.2" fill="#FFFFFF" xlink:href="#path-5"></use>
                                </g>
                            </g>
                        </g>
                    </g>
                </svg>
            </span>
            <span class="app-brand-text demo menu-text fw-bolder ms-2">RPS Admin</span>
        </a>

        <a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto d-block d-xl-none">
            <i class="ri-arrow-left-s-line"></i>
        </a>
    </div>

    <div class="menu-inner-shadow"></div>

    <!-- Dynamic Menu -->
    <ul class="menu-inner py-1" id="admin-menu">
        <?php foreach ($menuItems as $item): ?>
            <?php if ($item['type'] === 'header'): ?>
                <!-- Menu Header -->
                <li class="menu-header small text-uppercase">
                    <span class="menu-header-text"><?= esc($item['title']) ?></span>
                </li>
            <?php elseif ($item['type'] === 'item'): ?>
                <!-- Menu Item -->
                <li class="menu-item <?= isset($item['active']) && $item['active'] ? 'active' : '' ?> <?= !empty($item['children']) ? 'menu-item-submenu' : '' ?>">
                    <a href="<?= !empty($item['children']) ? 'javascript:void(0);' : base_url($item['url']) ?>" 
                       class="menu-link <?= !empty($item['children']) ? 'menu-toggle' : '' ?>"
                       <?= isset($item['onclick']) ? 'onclick="' . $item['onclick'] . '"' : '' ?>>
                        <i class="menu-icon <?= $item['icon'] ?>"></i>
                        <div data-i18n="<?= $item['title'] ?>"><?= esc($item['title']) ?></div>
                        <?php if ($item['badge']): ?>
                            <div class="badge bg-danger rounded-pill ms-auto"><?= esc($item['badge']) ?></div>
                        <?php endif; ?>
                        <?php if (!empty($item['children'])): ?>
                            <div class="menu-arrow">
                                <i class="ri-arrow-right-s-line"></i>
                            </div>
                        <?php endif; ?>
                    </a>
                    
                    <?php if (!empty($item['children'])): ?>
                        <!-- Submenu -->
                        <ul class="menu-sub">
                            <?php foreach ($item['children'] as $child): ?>
                                <li class="menu-item">
                                    <a href="<?= base_url($child['url']) ?>" class="menu-link">
                                        <div data-i18n="<?= $child['title'] ?>"><?= esc($child['title']) ?></div>
                                    </a>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </li>
            <?php endif; ?>
        <?php endforeach; ?>
    </ul>

    <!-- Menu Footer -->
    <div class="menu-footer">
        <div class="menu-footer-content">
            <small class="text-muted">
                <i class="ri-information-line me-1"></i>
                Version 1.0.0
            </small>
        </div>
    </div>
</aside>

<!-- Enhanced Menu Styles -->
<style>
.menu-item-submenu .menu-link {
    position: relative;
}

.menu-arrow {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
}

.menu-item-submenu.open .menu-arrow {
    transform: translateY(-50%) rotate(90deg);
}

.menu-sub {
    display: none;
    padding-left: 2rem;
    background: rgba(0, 0, 0, 0.02);
}

.menu-item-submenu.open .menu-sub {
    display: block;
}

.menu-sub .menu-link {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.menu-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    border-top: 1px solid #e0e0e0;
    background: #fff;
}

.menu-footer-content {
    text-align: center;
}

.badge {
    font-size: 0.625rem;
    padding: 0.25rem 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 1199.98px) {
    .layout-menu {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .layout-menu.show {
        transform: translateX(0);
    }
}

/* Menu animations */
.menu-item {
    transition: all 0.2s ease;
}

.menu-link:hover {
    background: rgba(105, 108, 255, 0.08);
    transform: translateX(2px);
}

.menu-item.active .menu-link {
    background: rgba(105, 108, 255, 0.12);
    color: #696cff;
    font-weight: 500;
}

.menu-item.active .menu-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #696cff;
}
</style>

<!-- Enhanced Menu JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeEnhancedMenu();
});

function initializeEnhancedMenu() {
    // Handle submenu toggles
    const submenuToggles = document.querySelectorAll('.menu-toggle');
    submenuToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const menuItem = this.closest('.menu-item-submenu');
            const isOpen = menuItem.classList.contains('open');
            
            // Close all other submenus
            document.querySelectorAll('.menu-item-submenu.open').forEach(item => {
                if (item !== menuItem) {
                    item.classList.remove('open');
                }
            });
            
            // Toggle current submenu
            menuItem.classList.toggle('open', !isOpen);
        });
    });

    // Handle mobile menu toggle
    const menuToggle = document.querySelector('.layout-menu-toggle');
    const layoutMenu = document.querySelector('.layout-menu');
    const layoutOverlay = document.querySelector('.layout-overlay');

    if (menuToggle) {
        menuToggle.addEventListener('click', function() {
            layoutMenu.classList.toggle('show');
            if (layoutOverlay) {
                layoutOverlay.classList.toggle('show');
            }
        });
    }

    // Close menu when clicking overlay
    if (layoutOverlay) {
        layoutOverlay.addEventListener('click', function() {
            layoutMenu.classList.remove('show');
            layoutOverlay.classList.remove('show');
        });
    }

    // Auto-expand active submenu
    const activeMenuItem = document.querySelector('.menu-item.active');
    if (activeMenuItem && activeMenuItem.closest('.menu-item-submenu')) {
        activeMenuItem.closest('.menu-item-submenu').classList.add('open');
    }

    // Add smooth scrolling to menu
    const menuInner = document.querySelector('.menu-inner');
    if (menuInner) {
        menuInner.style.scrollBehavior = 'smooth';
    }
}
</script>
