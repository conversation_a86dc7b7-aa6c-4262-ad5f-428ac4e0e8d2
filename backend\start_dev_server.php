<?php
/**
 * 🚀 Development Server Starter untuk FlutterFlow Integration
 * 
 * Script ini memulai server development dan memverifikasi konfigurasi API
 */

echo "🚀 Starting Development Server for FlutterFlow Integration\n";
echo str_repeat("=", 60) . "\n\n";

// Check if we're in the correct directory
if (!file_exists('spark')) {
    echo "❌ Error: Please run this script from the backend directory\n";
    echo "💡 Usage: cd backend && php start_dev_server.php\n";
    exit(1);
}

// Check if composer dependencies are installed
if (!file_exists('vendor/autoload.php')) {
    echo "📦 Installing Composer dependencies...\n";
    system('composer install');
    echo "\n";
}

// Check database configuration
echo "🔍 Checking database configuration...\n";
$envFile = '.env';
if (!file_exists($envFile)) {
    echo "⚠️  .env file not found. Creating from .env.example...\n";
    if (file_exists('env')) {
        copy('env', '.env');
        echo "✅ .env file created\n";
    } else {
        echo "❌ No .env.example or env file found\n";
        echo "💡 Please create .env file with database configuration\n";
    }
}

// Check if database is accessible
echo "🗄️  Testing database connection...\n";
try {
    $output = shell_exec('php spark db:table');
    if (strpos($output, 'Error') === false) {
        echo "✅ Database connection successful\n";
    } else {
        echo "⚠️  Database connection issue detected\n";
        echo "💡 Run: php setup_database.php to initialize database\n";
    }
} catch (Exception $e) {
    echo "⚠️  Could not test database connection\n";
}

echo "\n";

// Display server information
$host = '0.0.0.0';
$port = '8080';
$baseUrl = "http://localhost:$port";
$apiUrl = "$baseUrl/api/v1";

echo "📋 Server Configuration:\n";
echo "Host: $host\n";
echo "Port: $port\n";
echo "Base URL: $baseUrl\n";
echo "API URL: $apiUrl\n";
echo "Environment: development\n\n";

echo "📱 FlutterFlow Configuration:\n";
echo "API Base URL: $apiUrl\n";
echo "Authentication: Bearer Token (JWT)\n";
echo "Content-Type: application/json\n\n";

echo "🔌 Available API Endpoints:\n";
echo "• POST /auth/login - User authentication\n";
echo "• GET  /auth/profile - Get user profile\n";
echo "• GET  /users - List users\n";
echo "• GET  /faculties - List faculties\n";
echo "• GET  /study-programs - List study programs\n";
echo "• GET  /courses - List courses\n";
echo "• GET  /dashboard/stats - Dashboard statistics\n\n";

echo "🛠️  Development Tools:\n";
echo "• Verification: php verify_api_for_flutterflow.php\n";
echo "• Database setup: php setup_database.php\n";
echo "• Test auth: php test_auth.php\n\n";

// Start the server
echo "🚀 Starting CodeIgniter development server...\n";
echo "Press Ctrl+C to stop the server\n";
echo str_repeat("-", 60) . "\n";

// Use passthru to show real-time output
passthru("php spark serve --host=$host --port=$port");
?>
