<?= $this->extend('admin/layouts/blank') ?>

<?= $this->section('page_css') ?>
<style>
.auth-wrapper {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.auth-card {
    width: 100%;
    max-width: 400px;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.auth-header {
    text-align: center;
    padding: 2rem 2rem 1rem;
}

.auth-logo {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, #696cff 0%, #5f61e6 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.auth-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #5d596c;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: #a8b1bb;
    font-size: 0.875rem;
}

.auth-form {
    padding: 0 2rem 2rem;
}

.form-floating {
    margin-bottom: 1rem;
}

.form-floating .form-control {
    border: 1px solid #d9dee3;
    border-radius: 0.5rem;
    padding: 1rem 0.75rem 0.25rem;
    height: auto;
}

.form-floating .form-control:focus {
    border-color: #696cff;
    box-shadow: 0 0 0 0.2rem rgba(105, 108, 255, 0.25);
}

.form-floating label {
    color: #a8b1bb;
    padding: 1rem 0.75rem;
}

.btn-auth {
    width: 100%;
    padding: 0.75rem;
    border-radius: 0.5rem;
    font-weight: 500;
    background: linear-gradient(135deg, #696cff 0%, #5f61e6 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.btn-auth:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(105, 108, 255, 0.3);
    color: white;
}

.auth-footer {
    text-align: center;
    padding: 1rem 2rem 2rem;
    border-top: 1px solid #f0f0f0;
    margin-top: 1rem;
}

.auth-footer a {
    color: #696cff;
    text-decoration: none;
}

.auth-footer a:hover {
    text-decoration: underline;
}

.password-toggle {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #a8b1bb;
    cursor: pointer;
    z-index: 10;
}

.password-toggle:hover {
    color: #696cff;
}

.alert {
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="auth-wrapper">
    <div class="auth-card">
        <div class="auth-header">
            <div class="auth-logo">
                <i class="ri-admin-line"></i>
            </div>
            <h1 class="auth-title">Welcome Back!</h1>
            <p class="auth-subtitle">Sign in to your admin account to continue</p>
        </div>

        <form class="auth-form" method="POST" action="<?= base_url('admin/login') ?>">
            <?= csrf_field() ?>
            
            <div class="form-floating">
                <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" 
                       value="<?= old('email') ?>" required>
                <label for="email">
                    <i class="ri-mail-line me-2"></i>Email Address
                </label>
            </div>

            <div class="form-floating position-relative">
                <input type="password" class="form-control" id="password" name="password" placeholder="Password" required>
                <label for="password">
                    <i class="ri-lock-line me-2"></i>Password
                </label>
                <button type="button" class="password-toggle" onclick="togglePassword()">
                    <i class="ri-eye-line" id="password-icon"></i>
                </button>
            </div>

            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                    <label class="form-check-label" for="remember">
                        Remember me
                    </label>
                </div>
                <a href="#" class="text-decoration-none">Forgot password?</a>
            </div>

            <button type="submit" class="btn btn-auth">
                <i class="ri-login-box-line me-2"></i>Sign In
            </button>
        </form>

        <div class="auth-footer">
            <p class="mb-0">
                Don't have an account? 
                <a href="<?= base_url('admin/register') ?>">Create one here</a>
            </p>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('page_js') ?>
<script>
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const passwordIcon = document.getElementById('password-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.className = 'ri-eye-off-line';
    } else {
        passwordInput.type = 'password';
        passwordIcon.className = 'ri-eye-line';
    }
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.auth-form');
    
    form.addEventListener('submit', function(e) {
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        
        if (!email || !password) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            return false;
        }
        
        if (!isValidEmail(email)) {
            e.preventDefault();
            alert('Please enter a valid email address.');
            return false;
        }
        
        if (password.length < 6) {
            e.preventDefault();
            alert('Password must be at least 6 characters long.');
            return false;
        }
    });
});

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Auto-focus on first input
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('email').focus();
});
</script>
<?= $this->endSection() ?>
