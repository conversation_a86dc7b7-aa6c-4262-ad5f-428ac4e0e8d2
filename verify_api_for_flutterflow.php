<?php
echo "🔍 Verifying API for FlutterFlow Integration\n";
echo str_repeat("=", 50) . "\n";

// 1. Check if backend server is running
$backendUrl = 'http://localhost:8080';
$response = @file_get_contents($backendUrl, false, stream_context_create(['http' => ['timeout' => 5]]));

if ($response === false) {
    echo "❌ Backend server not running. Start with: php spark serve --host=0.0.0.0 --port=8080\n";
    exit(1);
}

echo "✅ Backend server is running\n";

// 2. Test API endpoints
$apiTests = [
    '/api/v1/auth/login' => 'POST',
    '/api/v1/auth/profile' => 'GET',
    '/api/v1/users' => 'GET',
    '/api/v1/faculties' => 'GET',
];

foreach ($apiTests as $endpoint => $method) {
    $url = $backendUrl . $endpoint;
    $context = stream_context_create([
        'http' => [
            'method' => $method,
            'header' => "Content-Type: application/json\r\n",
            'timeout' => 5,
            'ignore_errors' => true
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    $httpCode = isset($http_response_header[0]) ? $http_response_header[0] : 'No response';
    
    // Check if we get expected responses (200, 401, 422 are all valid)
    $isValid = preg_match('/HTTP\/\d\.\d\s+(200|401|422|404)/', $httpCode);
    
    echo ($isValid ? "✅" : "❌") . " $method $endpoint: $httpCode\n";
}

echo "\n📱 FlutterFlow Configuration:\n";
echo "Base URL: $backendUrl/api/v1\n";
echo "Auth Header: Authorization: Bearer {token}\n";
echo "Content-Type: application/json\n";
?>