/* Materio Admin Dashboard CSS */
/* Based on Materio Vue Template and Vuetify */

:root {
  --v-theme-primary: #696cff;
  --v-theme-secondary: #8592a3;
  --v-theme-success: #71dd37;
  --v-theme-info: #03c3ec;
  --v-theme-warning: #ffab00;
  --v-theme-error: #ff3e1d;
  --v-theme-surface: #fff;
  --v-theme-background: #f5f5f9;
  --v-theme-on-primary: #fff;
  --v-theme-on-secondary: #fff;
  --v-theme-on-success: #fff;
  --v-theme-on-info: #fff;
  --v-theme-on-warning: #fff;
  --v-theme-on-error: #fff;
  --v-theme-on-surface: #697a8d;
  --v-theme-on-background: #697a8d;
}

/* Layout */
.layout-wrapper {
  height: 100vh;
  overflow: hidden;
}

.layout-container {
  display: flex;
  height: 100%;
}

.layout-menu {
  width: 260px;
  background: #fff;
  border-right: 1px solid #d9dee3;
  transition: all 0.25s ease;
}

.layout-page {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.content-wrapper {
  flex: 1;
  overflow: auto;
  background: #f5f5f9;
}

/* Menu Styles */
.menu {
  padding: 0;
  margin: 0;
  list-style: none;
}

.menu-inner {
  padding: 0;
}

.menu-item {
  position: relative;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: #697a8d;
  text-decoration: none;
  transition: all 0.15s ease;
  border-radius: 0;
}

.menu-link:hover {
  color: var(--bs-primary);
  background: rgba(105, 108, 255, 0.04);
}

.menu-item.active .menu-link {
  color: var(--bs-primary);
  background: rgba(105, 108, 255, 0.08);
  font-weight: 500;
}

.menu-icon {
  margin-right: 0.75rem;
  font-size: 1.125rem;
  flex-shrink: 0;
}

.menu-header {
  padding: 1rem 1.5rem 0.5rem;
  margin-top: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.4px;
  color: #a8b1bb;
}

.menu-header:first-child {
  margin-top: 0;
}

/* App Brand */
.app-brand {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #d9dee3;
}

.app-brand-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #697a8d;
}

.app-brand-text {
  font-size: 1.25rem;
  font-weight: 700;
  margin-left: 0.5rem;
  color: #5d596c;
}

/* Navbar */
.layout-navbar {
  background: #fff !important;
  border-bottom: 1px solid #d9dee3;
  padding: 0 1.5rem;
  height: 60px;
  display: flex;
  align-items: center;
}

.navbar-nav-right {
  margin-left: auto;
}

/* Cards */
.card {
  border: 1px solid #d9dee3;
  border-radius: 0.375rem;
  box-shadow: 0 2px 6px 0 rgba(67, 89, 113, 0.12);
}

.card-header {
  background: transparent;
  border-bottom: 1px solid #d9dee3;
  padding: 1.5rem;
}

.card-body {
  padding: 1.5rem;
}

/* Buttons */
.btn {
  border-radius: 0.375rem;
  font-weight: 500;
  padding: 0.4375rem 1.25rem;
}

.btn-primary {
  background: var(--bs-primary);
  border-color: var(--bs-primary);
}

.btn-primary:hover {
  background: #5f61e6;
  border-color: #5f61e6;
}

/* Forms */
.form-control {
  border: 1px solid #d9dee3;
  border-radius: 0.375rem;
  padding: 0.4375rem 0.875rem;
}

.form-control:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.2rem rgba(105, 108, 255, 0.25);
}

/* Tables */
.table {
  border-color: #d9dee3;
}

.table th {
  border-bottom: 2px solid #d9dee3;
  font-weight: 600;
  color: #5d596c;
}

/* Alerts */
.alert {
  border-radius: 0.375rem;
  border: none;
}

.alert-success {
  background: rgba(113, 221, 55, 0.16);
  color: #71dd37;
}

.alert-danger {
  background: rgba(255, 62, 29, 0.16);
  color: #ff3e1d;
}

.alert-warning {
  background: rgba(255, 171, 0, 0.16);
  color: #ffab00;
}

.alert-info {
  background: rgba(3, 195, 236, 0.16);
  color: #03c3ec;
}

/* Footer */
.content-footer {
  background: #fff;
  border-top: 1px solid #d9dee3;
  padding: 1rem 1.5rem;
  margin-top: auto;
}

/* Responsive */
@media (max-width: 1199.98px) {
  .layout-menu {
    position: fixed;
    top: 0;
    left: -260px;
    height: 100vh;
    z-index: 1000;
  }
  
  .layout-menu.show {
    left: 0;
  }
  
  .layout-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(67, 89, 113, 0.6);
    z-index: 999;
    display: none;
  }
  
  .layout-overlay.show {
    display: block;
  }
}

/* Utilities */
.text-primary {
  color: var(--bs-primary) !important;
}

.bg-primary {
  background-color: var(--bs-primary) !important;
}

.border-primary {
  border-color: var(--bs-primary) !important;
}

/* Custom Components */
.stats-card {
  background: linear-gradient(135deg, var(--bs-primary) 0%, #5f61e6 100%);
  color: white;
  border: none;
}

.stats-card .card-body {
  padding: 2rem;
}

.stats-number {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stats-label {
  font-size: 0.875rem;
  opacity: 0.8;
}

/* Animation */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Search Styles */
.admin-search {
  min-width: 300px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  color: #697a8d;
}

.admin-search:focus {
  background: rgba(255, 255, 255, 0.9);
  border-color: var(--v-theme-primary) !important;
  box-shadow: 0 0 0 0.2rem rgba(105, 108, 255, 0.25);
  color: #5d596c;
}

.search-results-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
  margin-top: 0.5rem;
}

.search-results-header,
.search-results-footer {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f0f0f0;
  background: #f8f9fa;
}

.search-results-footer {
  border-bottom: none;
  border-top: 1px solid #f0f0f0;
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: #5d596c;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.search-result-item:hover {
  background: #f8f9fa;
  color: var(--v-theme-primary);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(105, 108, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: var(--v-theme-primary);
}

.search-result-content {
  flex: 1;
}

.search-result-title {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.search-result-category {
  font-size: 0.75rem;
  color: #a8b1bb;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.search-no-results {
  padding: 2rem 1rem;
  text-align: center;
  color: #a8b1bb;
}

/* Enhanced Menu Styles */
.menu-item-submenu .menu-link {
  position: relative;
}

.menu-arrow {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  transition: transform 0.3s ease;
}

.menu-item-submenu.open .menu-arrow {
  transform: translateY(-50%) rotate(90deg);
}

.menu-sub {
  display: none;
  padding-left: 2rem;
  background: rgba(0, 0, 0, 0.02);
}

.menu-item-submenu.open .menu-sub {
  display: block;
  animation: slideDown 0.3s ease;
}

.menu-sub .menu-link {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

/* Notification Badge */
.badge-notifications {
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 0.625rem;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Quick Actions Dropdown */
.dropdown-menu {
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
}

.dropdown-header {
  font-weight: 600;
  color: #5d596c;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
}

.dropdown-item {
  padding: 0.75rem 1rem;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background: rgba(105, 108, 255, 0.08);
  color: var(--v-theme-primary);
}

/* Breadcrumb Enhancements */
.breadcrumb-nav {
  margin-bottom: 1.5rem;
}

.breadcrumb {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Mobile Menu Enhancements */
@media (max-width: 1199.98px) {
  .admin-search {
    min-width: 200px;
  }

  .search-results-dropdown {
    left: -100px;
    right: -100px;
  }
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.menu-item {
  transition: all 0.2s ease;
}

.menu-link:hover {
  background: rgba(105, 108, 255, 0.08);
  transform: translateX(2px);
}

.menu-item.active .menu-link {
  background: rgba(105, 108, 255, 0.12);
  color: var(--v-theme-primary);
  font-weight: 500;
  position: relative;
}

.menu-item.active .menu-link::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--v-theme-primary);
}

/* Keyboard Shortcuts Indicator */
.keyboard-shortcut {
  font-size: 0.75rem;
  background: rgba(0, 0, 0, 0.1);
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  margin-left: auto;
}

/* Loading States */
.menu-loading {
  opacity: 0.6;
  pointer-events: none;
}

.search-loading::after {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid #e0e0e0;
  border-top: 2px solid var(--v-theme-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-left: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --bs-body-bg: #25293c;
    --bs-body-color: #a8b1bb;
  }

  .search-results-dropdown {
    background: #2b2c40;
    border-color: #3a3b5c;
  }

  .search-result-item {
    color: #a8b1bb;
  }

  .search-result-item:hover {
    background: #3a3b5c;
  }
}
