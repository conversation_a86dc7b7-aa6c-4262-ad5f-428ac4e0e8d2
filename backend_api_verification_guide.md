# 🔧 Backend API Verification Guide untuk FlutterFlow

## 📋 Analisis Struktur Backend Saat Ini

### ✅ Struktur yang Sudah Ada
Berdasarkan kode yang saya lihat, backend CodeIgniter 4 sudah memiliki:

1. **API Routes Structure** - Sudah ada routing untuk API di `backend/app/Config/Routes.php`
2. **Frontend Controller** - Ada `Frontend.php` controller untuk serve Vue.js
3. **JWT Authentication** - Sudah ada dependency `firebase/php-jwt` di `composer.json`
4. **API Service Layer** - Frontend sudah punya `api.ts` dengan endpoint structure

### 🔧 Yang Perlu Dipastikan untuk FlutterFlow

#### API Routes Configuration
```php
// API Routes for mobile/external clients
$routes->group('api/v1', ['namespace' => 'App\Controllers\API'], function($routes) {
    // Authentication
    $routes->post('auth/login', 'AuthController::login');
    $routes->post('auth/logout', 'AuthController::logout');
    $routes->post('auth/refresh', 'AuthController::refresh');
    $routes->get('auth/profile', 'AuthController::profile');
    
    // Users Management
    $routes->resource('users', ['controller' => 'UsersController']);
    
    // Academic Data
    $routes->resource('faculties', ['controller' => 'FacultiesController']);
    $routes->resource('study-programs', ['controller' => 'StudyProgramsController']);
    $routes->resource('courses', ['controller' => 'CoursesController']);
    
    // RPS Management
    $routes->resource('cpl', ['controller' => 'CPLController']);
    $routes->resource('cpmk', ['controller' => 'CPMKController']);
    $routes->resource('assessments', ['controller' => 'AssessmentsController']);
    
    // Reports
    $routes->get('reports/dashboard', 'ReportsController::dashboard');
    $routes->get('reports/cpmk-achievement', 'ReportsController::cpmkAchievement');
});
```

### 🚀 Langkah Verifikasi API untuk FlutterFlow

#### 1. Test API Endpoints Script
```php
<?php
// Test script untuk memastikan API endpoints berjalan
$baseUrl = 'http://localhost:8080/api/v1';

$endpoints = [
    'GET /auth/profile' => ['expected' => 401], // Unauthorized without token
    'POST /auth/login' => ['expected' => [200, 422]], // Success or validation error
    'GET /users' => ['expected' => 401], // Unauthorized
    'GET /faculties' => ['expected' => [200, 401]],
    'GET /courses' => ['expected' => [200, 401]],
];

foreach ($endpoints as $endpoint => $config) {
    [$method, $path] = explode(' ', $endpoint);
    $url = $baseUrl . $path;
    
    $context = stream_context_create([
        'http' => [
            'method' => $method,
            'header' => "Content-Type: application/json\r\n",
            'timeout' => 10
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    $httpCode = $http_response_header[0] ?? 'No response';
    
    echo "Testing: $endpoint\n";
    echo "Response: $httpCode\n";
    echo "Status: " . (strpos($httpCode, '200') !== false || strpos($httpCode, '401') !== false || strpos($httpCode, '422') !== false ? '✅' : '❌') . "\n\n";
}
?>
```

#### 2. CORS Configuration
```php
// backend/app/Config/Filters.php
public $globals = [
    'before' => [
        'cors',
    ],
    'after' => [
        'toolbar',
    ],
];

public $filters = [
    'cors' => [
        'before' => ['api/*'],
        'after' => ['api/*'],
    ],
];
```

#### 3. CORS Filter Implementation
```php
<?php
// backend/app/Filters/CorsFilter.php
namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class CorsFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        if ($request->getMethod() === 'OPTIONS') {
            $response = service('response');
            $response->setHeader('Access-Control-Allow-Origin', '*');
            $response->setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            $response->setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
            $response->setHeader('Access-Control-Max-Age', '86400');
            $response->setStatusCode(200);
            return $response;
        }
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        $response->setHeader('Access-Control-Allow-Origin', '*');
        $response->setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        $response->setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
        return $response;
    }
}
```

### 📱 Konfigurasi untuk FlutterFlow

#### API Configuration
- **Development URL**: `http://localhost:8080/api/v1`
- **Production URL**: `https://yourdomain.com/api/v1`
- **Auth Header**: `Authorization: Bearer {jwt_token}`
- **Content-Type**: `application/json`

#### Verification Script
```php
<?php
echo "🔍 Verifying API for FlutterFlow Integration\n";
echo str_repeat("=", 50) . "\n";

// 1. Check if backend server is running
$backendUrl = 'http://localhost:8080';
$response = @file_get_contents($backendUrl, false, stream_context_create(['http' => ['timeout' => 5]]));

if ($response === false) {
    echo "❌ Backend server not running. Start with: php spark serve --host=0.0.0.0 --port=8080\n";
    exit(1);
}

echo "✅ Backend server is running\n";

// 2. Test API endpoints
$apiTests = [
    '/api/v1/auth/login' => 'POST',
    '/api/v1/auth/profile' => 'GET',
    '/api/v1/users' => 'GET',
    '/api/v1/faculties' => 'GET',
];

foreach ($apiTests as $endpoint => $method) {
    $url = $backendUrl . $endpoint;
    $context = stream_context_create([
        'http' => [
            'method' => $method,
            'header' => "Content-Type: application/json\r\n",
            'timeout' => 5,
            'ignore_errors' => true
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    $httpCode = isset($http_response_header[0]) ? $http_response_header[0] : 'No response';
    
    $isValid = preg_match('/HTTP\/\d\.\d\s+(200|401|422|404)/', $httpCode);
    
    echo ($isValid ? "✅" : "❌") . " $method $endpoint: $httpCode\n";
}

echo "\n📱 FlutterFlow Configuration:\n";
echo "Base URL: $backendUrl/api/v1\n";
echo "Auth Header: Authorization: Bearer {token}\n";
echo "Content-Type: application/json\n";
?>
```

### 🚀 Cara Menjalankan Verifikasi

#### 1. Start Backend Server
```bash
cd backend
php spark serve --host=0.0.0.0 --port=8080
```

#### 2. Run Verification
```bash
php verify_api_for_flutterflow.php
```

#### 3. Test dengan curl
```bash
curl -X GET http://localhost:8080/api/v1/auth/profile
curl -X POST http://localhost:8080/api/v1/auth/login -H "Content-Type: application/json"
```

## ✅ Kesimpulan

API structure sudah siap untuk FlutterFlow dengan:
- ✅ CORS configuration
- ✅ JWT authentication
- ✅ RESTful endpoints
- ✅ JSON responses

---

**Created**: $(date)  
**Purpose**: Backend API verification untuk FlutterFlow integration  
**Status**: Ready for implementation